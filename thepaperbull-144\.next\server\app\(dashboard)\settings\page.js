(()=>{var e={};e.id=670,e.ids=[670],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30309:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\(dashboard)\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\settings\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40807:(e,r,t)=>{Promise.resolve().then(t.bind(t,30309))},44254:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=t(65239),s=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["(dashboard)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,30309)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,75870))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"D:\\step-by-step\\thepaperbull-144\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"D:\\step-by-step\\thepaperbull-144\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(t.bind(t,75870))).default(e)],manifest:void 0}}]}.children,c=["D:\\step-by-step\\thepaperbull-144\\app\\(dashboard)\\settings\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},58869:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71071:(e,r,t)=>{Promise.resolve().then(t.bind(t,95386))},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95386:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>S});var a=t(60687),s=t(43210),i=t(16189),n=t(62688);let l=(0,n.A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var o=t(84027),d=t(58869),c=t(97051),m=t(363);let x=(0,n.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var g=t(85778),u=t(99891);let p=(0,n.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var h=t(21134),b=t(25878);t(21643);var y=t(83427);t(50227),t(80043);var f=t(95216);let v=(0,n.A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);var j=t(11860),k=t(13964);function N({content:e,children:r,position:t="top",className:i="",delay:n=300}){let[l,o]=(0,s.useState)(!1),[d,c]=(0,s.useState)(t),m=(0,s.useRef)(),x=(0,s.useRef)(null),g=(0,s.useRef)(null),u=()=>{m.current&&clearTimeout(m.current),m.current=setTimeout(()=>{o(!0),h()},n)},p=()=>{m.current&&clearTimeout(m.current),o(!1)},h=()=>{if(!x.current||!g.current)return;let e=x.current,r=g.current.getBoundingClientRect(),a=e.getBoundingClientRect(),s={width:window.innerWidth,height:window.innerHeight},i=t;switch(t){case"top":r.top-a.height<0&&(i="bottom");break;case"bottom":r.bottom+a.height>s.height&&(i="top");break;case"left":r.left-a.width<0&&(i="right");break;case"right":r.right+a.width>s.width&&(i="left")}c(i)};return(0,a.jsxs)("div",{ref:g,className:`relative inline-block ${i}`,onMouseEnter:u,onMouseLeave:p,onFocus:u,onBlur:p,children:[r,l&&(0,a.jsxs)("div",{ref:x,className:(()=>{let e="absolute z-50 px-3 py-2 text-sm bg-gray-900 text-white rounded-lg shadow-lg pointer-events-none transition-all duration-200";switch(d){case"top":return`${e} bottom-full left-1/2 transform -translate-x-1/2 mb-2`;case"bottom":return`${e} top-full left-1/2 transform -translate-x-1/2 mt-2`;case"left":return`${e} right-full top-1/2 transform -translate-y-1/2 mr-2`;case"right":return`${e} left-full top-1/2 transform -translate-y-1/2 ml-2`;default:return e}})(),style:{opacity:+!!l,transform:l?"scale(1)":"scale(0.95)"},children:[e,(0,a.jsx)("div",{className:(()=>{let e="absolute w-2 h-2 bg-gray-900 transform rotate-45";switch(d){case"top":return`${e} top-full left-1/2 transform -translate-x-1/2 -translate-y-1/2`;case"bottom":return`${e} bottom-full left-1/2 transform -translate-x-1/2 translate-y-1/2`;case"left":return`${e} left-full top-1/2 transform -translate-x-1/2 -translate-y-1/2`;case"right":return`${e} right-full top-1/2 transform translate-x-1/2 -translate-y-1/2`;default:return e}})()})]})]})}let w=[{id:"bull-trader",name:"Bull Trader",emoji:"\uD83D\uDC02",bgColor:"bg-gradient-to-br from-green-500 to-green-600",description:"Optimistic market outlook",category:"Market Sentiment"},{id:"bear-trader",name:"Bear Trader",emoji:"\uD83D\uDC3B",bgColor:"bg-gradient-to-br from-red-500 to-red-600",description:"Cautious and defensive",category:"Market Sentiment"},{id:"diamond-hands",name:"Diamond Hands",emoji:"\uD83D\uDC8E",bgColor:"bg-gradient-to-br from-blue-500 to-purple-600",description:"Long-term HODL strategy",category:"Investment Style"},{id:"rocket-trader",name:"Rocket Trader",emoji:"\uD83D\uDE80",bgColor:"bg-gradient-to-br from-blue-600 to-indigo-600",description:"Aiming for the moon",category:"Investment Style"},{id:"chart-master",name:"Chart Master",emoji:"\uD83D\uDCC8",bgColor:"bg-gradient-to-br from-emerald-500 to-teal-600",description:"Technical analysis expert",category:"Trading Style"},{id:"lightning-fast",name:"Lightning Fast",emoji:"⚡",bgColor:"bg-gradient-to-br from-yellow-400 to-orange-500",description:"High-frequency trading",category:"Trading Style"},{id:"whale-trader",name:"Whale Trader",emoji:"\uD83D\uDC0B",bgColor:"bg-gradient-to-br from-blue-700 to-blue-800",description:"Big capital movements",category:"Capital Size"},{id:"crypto-king",name:"Crypto King",emoji:"\uD83D\uDC51",bgColor:"bg-gradient-to-br from-yellow-500 to-amber-600",description:"Cryptocurrency royalty",category:"Expertise"},{id:"ninja-trader",name:"Ninja Trader",emoji:"\uD83E\uDD77",bgColor:"bg-gradient-to-br from-gray-700 to-gray-800",description:"Stealth market entry",category:"Trading Style"},{id:"robot-trader",name:"Robot Trader",emoji:"\uD83E\uDD16",bgColor:"bg-gradient-to-br from-slate-500 to-slate-600",description:"Algorithmic strategies",category:"Trading Style"},{id:"fire-trader",name:"Fire Trader",emoji:"\uD83D\uDD25",bgColor:"bg-gradient-to-br from-orange-500 to-red-500",description:"Hot streak performer",category:"Performance"},{id:"target-trader",name:"Target Trader",emoji:"\uD83C\uDFAF",bgColor:"bg-gradient-to-br from-red-600 to-pink-600",description:"Precision execution",category:"Trading Style"},{id:"brain-trader",name:"Brain Trader",emoji:"\uD83E\uDDE0",bgColor:"bg-gradient-to-br from-purple-500 to-pink-500",description:"Strategic intelligence",category:"Expertise"},{id:"crystal-ball",name:"Fortune Teller",emoji:"\uD83D\uDD2E",bgColor:"bg-gradient-to-br from-purple-600 to-indigo-600",description:"Market prediction",category:"Expertise"},{id:"money-maker",name:"Money Maker",emoji:"\uD83D\uDCB0",bgColor:"bg-gradient-to-br from-green-600 to-emerald-600",description:"Profit maximization",category:"Performance"},{id:"star-trader",name:"Star Trader",emoji:"⭐",bgColor:"bg-gradient-to-br from-amber-500 to-yellow-500",description:"Rising market star",category:"Performance"}];function C({selectedAvatar:e,onSelect:r,className:t=""}){let[i,n]=(0,s.useState)(!1),l=w.find(r=>r.id===e)||w[0],o=e=>{r(e),n(!1)},d=w.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return(0,a.jsxs)("div",{className:`${t}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6 p-6 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-800/30 hover:shadow-lg transition-all duration-300",children:[(0,a.jsx)("div",{className:`w-32 h-32 rounded-full ${l.bgColor} flex items-center justify-center text-6xl shadow-xl cursor-pointer hover:shadow-2xl hover:scale-105 transition-all duration-300 border-4 border-white dark:border-gray-700`,onClick:()=>n(!0),children:l.emoji}),(0,a.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-bold text-2xl text-gray-900 dark:text-gray-100 mb-1",children:l.name}),(0,a.jsx)("div",{className:"text-base text-gray-600 dark:text-gray-400 mb-2 leading-relaxed",children:l.description}),(0,a.jsx)("div",{className:"inline-flex items-center text-sm text-primary font-semibold bg-primary/10 px-3 py-1 rounded-full",children:l.category})]}),(0,a.jsxs)("button",{onClick:()=>n(!0),className:"inline-flex items-center px-6 py-3 text-base font-semibold text-white bg-primary hover:bg-primary/90 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105",children:[(0,a.jsx)(v,{className:"w-5 h-5 mr-2"}),"Change Avatar"]})]})]}),i&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Choose Your Trading Avatar"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Select an avatar that represents your trading style"})]}),(0,a.jsx)("button",{onClick:()=>n(!1),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:(0,a.jsx)(j.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:Object.entries(d).map(([r,t])=>(0,a.jsxs)("div",{className:"mb-10 last:mb-0",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 border-b border-gray-200 dark:border-gray-700 pb-3",children:r}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map(r=>(0,a.jsx)(N,{content:(0,a.jsxs)("div",{className:"text-center max-w-xs",children:[(0,a.jsx)("div",{className:"font-semibold text-white mb-1",children:r.name}),(0,a.jsx)("div",{className:"text-gray-200 text-xs mb-2",children:r.description}),(0,a.jsxs)("div",{className:"text-primary-200 text-xs font-medium",children:["Category: ",r.category]})]}),position:"top",delay:500,children:(0,a.jsxs)("div",{className:`relative cursor-pointer group p-8 rounded-2xl border-2 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 ${e===r.id?"border-primary bg-primary/15 shadow-xl scale-105 ring-2 ring-primary/30":"border-gray-200 dark:border-gray-700 hover:border-primary/60 bg-white dark:bg-gray-800/50 hover:bg-gray-50 dark:hover:bg-gray-800/70"}`,onClick:()=>o(r.id),children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:`w-28 h-28 rounded-full ${r.bgColor} flex items-center justify-center text-5xl shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110 mb-6 border-4 border-white dark:border-gray-700 group-hover:border-primary/30`,children:r.emoji}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"font-bold text-lg text-gray-900 dark:text-gray-100 group-hover:text-primary transition-colors",children:r.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 leading-relaxed px-2",children:r.description}),(0,a.jsx)("div",{className:"inline-flex items-center text-xs text-primary font-semibold bg-primary/15 px-3 py-1.5 rounded-full border border-primary/20",children:r.category})]})]}),e===r.id&&(0,a.jsx)("div",{className:"absolute -top-3 -right-3 w-10 h-10 bg-primary rounded-full flex items-center justify-center shadow-xl border-3 border-white dark:border-gray-900 animate-pulse",children:(0,a.jsx)(k.A,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"})]})},r.id))})]},r))}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[w.length," professional trading avatars available"]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>n(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>n(!1),className:"px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors",children:"Done"})]})]})]})})]})}function S(){(0,i.useSearchParams)();let[e,r]=(0,s.useState)("account"),{theme:t,setTheme:n,colorScheme:v,setColorScheme:j}=(0,b.D)(),{user:k}=function(){let e=(0,i.useRouter)(),[r,t]=(0,s.useState)(null),[a,n]=(0,s.useState)(!0);return{user:r,loading:a,isAuthenticated:!!r,login:async(e,r)=>{try{return await (0,f.Ru)(e,r)}catch(e){throw console.error("Login error:",e),e}},loginWithGoogle:async()=>{try{return await (0,f.G6)()}catch(e){throw console.error("Google login error:",e),e}},logout:async()=>{try{await (0,f.signOutUser)(),e.push("/login")}catch(e){throw console.error("Logout error:",e),e}}}}(),[N,w]=(0,s.useState)(null),[S,A]=(0,s.useState)(!0),[P,_]=(0,s.useState)(!1),[T,$]=(0,s.useState)({name:"",email:"",phone:"",country:"us",bio:"",avatar:"bull-trader"}),[M,q]=(0,s.useState)({email:!0,push:!0,trading_alerts:!0,price_alerts:!0,news_updates:!1}),[D,E]=(0,s.useState)({default_leverage:10,risk_management:!0,auto_close_positions:!1,confirmation_dialogs:!0}),G=async()=>{if(!k){alert("Please sign in to save settings.");return}if(!T.name.trim()){alert("Name is required.");return}_(!0);try{console.log("Saving account settings for user:",k.uid),console.log("Data to save:",{name:T.name,phone:T.phone,country:T.country,bio:T.bio,avatar:T.avatar}),await y.b.updateUserProfile(k.uid,{name:T.name.trim(),phone:T.phone.trim(),country:T.country,bio:T.bio.trim(),avatar:T.avatar}),console.log("Account settings saved successfully"),alert("Account settings saved successfully!")}catch(e){console.error("Error saving account settings:",e),alert(`Failed to save account settings: ${e.message||"Unknown error"}. Please try again.`)}finally{_(!1)}},F=async()=>{if(k){_(!0);try{await y.b.updateUserProfile(k.uid,{settings:{...N?.settings,notifications:M}}),alert("Notification settings saved successfully!")}catch(e){console.error("Error saving notification settings:",e),alert("Failed to save notification settings. Please try again.")}finally{_(!1)}}},L=async()=>{if(k){_(!0);try{await y.b.updateUserProfile(k.uid,{settings:{...N?.settings,trading:D}}),alert("Trading settings saved successfully!")}catch(e){console.error("Error saving trading settings:",e),alert("Failed to save trading settings. Please try again.")}finally{_(!1)}}},R=(e,r)=>{$(t=>({...t,[e]:r}))};return S?(0,a.jsx)("div",{className:"p-4 flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l,{className:"h-6 w-6 animate-spin"}),(0,a.jsx)("span",{children:"Loading settings..."})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"If this takes too long, please try refreshing the page"})]})}):S||N||k?(0,a.jsxs)("div",{className:"p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-4 sm:mb-6",children:(0,a.jsxs)("div",{className:"flex items-center mb-3 md:mb-0",children:[(0,a.jsx)("div",{className:"bg-emerald-100 rounded-full p-2 mr-3 dark:bg-emerald-900",children:(0,a.jsx)(o.A,{className:"h-5 w-5 sm:h-6 sm:w-6 text-emerald-600 dark:text-emerald-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl sm:text-2xl font-bold",children:"Settings"}),(0,a.jsx)("p",{className:"text-gray-500 text-xs sm:text-sm dark:text-gray-400",children:"Manage your account preferences"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsx)("div",{className:"lg:hidden mb-4",children:(0,a.jsx)("div",{className:"flex overflow-x-auto pb-2 gap-2",children:[{id:"account",label:"Account",icon:d.A},{id:"notifications",label:"Notifications",icon:c.A},{id:"appearance",label:"Appearance",icon:m.A},{id:"language",label:"Language",icon:x},{id:"payment",label:"Payment",icon:g.A},{id:"security",label:"Security",icon:u.A}].map(t=>{let s=t.icon;return(0,a.jsxs)("button",{onClick:()=>r(t.id),className:`flex flex-col items-center px-3 py-2 rounded-lg whitespace-nowrap text-xs font-medium ${e===t.id?"bg-primary text-primary-foreground":"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(s,{className:"h-4 w-4 mb-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:t.label}),(0,a.jsx)("span",{className:"sm:hidden",children:t.label.slice(0,4)})]},t.id)})})}),(0,a.jsxs)("div",{className:"hidden lg:block bg-white rounded-lg border overflow-hidden dark:bg-gray-800 dark:border-gray-700",children:[(0,a.jsx)("div",{className:"p-4 border-b dark:border-gray-700",children:(0,a.jsx)("h2",{className:"font-medium",children:"Settings"})}),(0,a.jsxs)("nav",{className:"p-2",children:[(0,a.jsxs)("button",{onClick:()=>r("account"),className:`flex items-center w-full px-3 py-2 rounded-md text-left ${"account"===e?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Account"})]}),(0,a.jsxs)("button",{onClick:()=>r("notifications"),className:`flex items-center w-full px-3 py-2 rounded-md text-left ${"notifications"===e?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Notifications"})]}),(0,a.jsxs)("button",{onClick:()=>r("appearance"),className:`flex items-center w-full px-3 py-2 rounded-md text-left ${"appearance"===e?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Appearance"})]}),(0,a.jsxs)("button",{onClick:()=>r("language"),className:`flex items-center w-full px-3 py-2 rounded-md text-left ${"language"===e?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Language & Region"})]}),(0,a.jsxs)("button",{onClick:()=>r("payment"),className:`flex items-center w-full px-3 py-2 rounded-md text-left ${"payment"===e?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Payment Methods"})]}),(0,a.jsxs)("button",{onClick:()=>r("security"),className:`flex items-center w-full px-3 py-2 rounded-md text-left ${"security"===e?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"}`,children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Security"})]})]})]})]}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg border p-4 sm:p-6 dark:bg-gray-800 dark:border-gray-700",children:["account"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-4 sm:mb-6",children:"Account Settings"}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 sm:mb-3",children:"Profile Picture"}),(0,a.jsx)(C,{selectedAvatar:T.avatar,onSelect:e=>R("avatar",e)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fullName",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Full Name"}),(0,a.jsx)("input",{type:"text",id:"fullName",value:T.name,onChange:e=>R("name",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email Address"}),(0,a.jsx)("input",{type:"email",id:"email",value:T.email,disabled:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:border-gray-600 dark:text-gray-400",title:"Email cannot be changed"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",id:"phone",value:T.phone,onChange:e=>R("phone",e.target.value),placeholder:"+****************",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"country",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Country"}),(0,a.jsxs)("select",{id:"country",value:T.country,onChange:e=>R("country",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:"us",children:"United States"}),(0,a.jsx)("option",{value:"ca",children:"Canada"}),(0,a.jsx)("option",{value:"uk",children:"United Kingdom"}),(0,a.jsx)("option",{value:"au",children:"Australia"}),(0,a.jsx)("option",{value:"de",children:"Germany"}),(0,a.jsx)("option",{value:"fr",children:"France"}),(0,a.jsx)("option",{value:"jp",children:"Japan"}),(0,a.jsx)("option",{value:"kr",children:"South Korea"}),(0,a.jsx)("option",{value:"sg",children:"Singapore"}),(0,a.jsx)("option",{value:"hk",children:"Hong Kong"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bio",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Bio"}),(0,a.jsx)("textarea",{id:"bio",rows:3,value:T.bio,onChange:e=>R("bio",e.target.value),placeholder:"Tell us about yourself...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"})]}),(0,a.jsx)("div",{className:"pt-3 sm:pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{onClick:G,disabled:P,className:"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md text-xs sm:text-sm font-medium hover:bg-primary/90 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[P?(0,a.jsx)(l,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin"}):(0,a.jsx)(p,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),P?"Saving...":"Save Changes"]})})]})]}),"notifications"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-4 sm:mb-6",children:"Notification Settings"}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium mb-2 sm:mb-3",children:"Email Notifications"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start sm:items-center justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"Price Alerts"}),(0,a.jsx)("div",{className:"text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:"Get notified when prices change significantly"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:M.price_alerts,onChange:()=>q({...M,price_alerts:!M.price_alerts}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Account Activity"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified about sign-ins and security alerts"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:M.email,onChange:()=>q({...M,email:!M.email}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"Push Notifications"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Price Alerts"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified when prices change significantly"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:M.push,onChange:()=>q({...M,push:!M.push}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Trading Activity"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified about your trades and orders"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:M.trading_alerts,onChange:()=>q({...M,trading_alerts:!M.trading_alerts}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"SMS Notifications"}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Security Alerts"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified about important security events"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:M.news_updates,onChange:()=>q({...M,news_updates:!M.news_updates}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})})]}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{onClick:F,disabled:P,className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[P?(0,a.jsx)(l,{className:"h-4 w-4 mr-2 animate-spin"}):(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),P?"Saving...":"Save Changes"]})})]})]}),"appearance"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-4 sm:mb-6",children:"Appearance Settings"}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium mb-2 sm:mb-3",children:"Theme"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:`border rounded-lg p-3 sm:p-4 cursor-pointer dark:border-gray-700 ${"light"===t?"ring-2 ring-primary":""}`,onClick:()=>n("light"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2 sm:mb-3",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"Light"}),(0,a.jsx)(h.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-yellow-500"})]}),(0,a.jsx)("div",{className:"h-16 sm:h-20 bg-white border rounded-md dark:border-gray-700"})]}),(0,a.jsxs)("div",{className:`border rounded-lg p-3 sm:p-4 cursor-pointer dark:border-gray-700 ${"dark"===t?"ring-2 ring-primary":""}`,onClick:()=>n("dark"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2 sm:mb-3",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"Dark"}),(0,a.jsx)(m.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-gray-700 dark:text-gray-300"})]}),(0,a.jsx)("div",{className:"h-16 sm:h-20 bg-gray-900 rounded-md"})]}),(0,a.jsxs)("div",{className:`border rounded-lg p-3 sm:p-4 cursor-pointer dark:border-gray-700 ${"system"===t?"ring-2 ring-primary":""}`,onClick:()=>n("system"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2 sm:mb-3",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"System"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-yellow-500"}),(0,a.jsx)("span",{className:"mx-1 text-xs sm:text-sm",children:"/"}),(0,a.jsx)(m.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-gray-700 dark:text-gray-300"})]})]}),(0,a.jsx)("div",{className:"h-16 sm:h-20 bg-gradient-to-r from-white to-gray-900 rounded-md"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium mb-2 sm:mb-3",children:"Color Scheme"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:`border rounded-lg p-2 cursor-pointer dark:border-gray-700 ${"emerald"===v?"ring-2 ring-primary":""}`,onClick:()=>j("emerald"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-emerald-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Emerald"})]}),(0,a.jsxs)("div",{className:`border rounded-lg p-2 cursor-pointer dark:border-gray-700 ${"blue"===v?"ring-2 ring-primary":""}`,onClick:()=>j("blue"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-blue-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Blue"})]}),(0,a.jsxs)("div",{className:`border rounded-lg p-2 cursor-pointer dark:border-gray-700 ${"purple"===v?"ring-2 ring-primary":""}`,onClick:()=>j("purple"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-purple-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Purple"})]}),(0,a.jsxs)("div",{className:`border rounded-lg p-2 cursor-pointer dark:border-gray-700 ${"orange"===v?"ring-2 ring-primary":""}`,onClick:()=>j("orange"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-orange-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Orange"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"Trading Preferences"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Default Leverage"}),(0,a.jsxs)("select",{value:D.default_leverage,onChange:e=>E({...D,default_leverage:parseInt(e.target.value)}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:1,children:"1x"}),(0,a.jsx)("option",{value:2,children:"2x"}),(0,a.jsx)("option",{value:5,children:"5x"}),(0,a.jsx)("option",{value:10,children:"10x"}),(0,a.jsx)("option",{value:20,children:"20x"}),(0,a.jsx)("option",{value:50,children:"50x"}),(0,a.jsx)("option",{value:100,children:"100x"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Risk Management"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Enable automatic risk management features"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.risk_management,onChange:()=>E({...D,risk_management:!D.risk_management}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Confirmation Dialogs"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Show confirmation before placing orders"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.confirmation_dialogs,onChange:()=>E({...D,confirmation_dialogs:!D.confirmation_dialogs}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})]})]})]}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{onClick:L,disabled:P,className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[P?(0,a.jsx)(l,{className:"h-4 w-4 mr-2 animate-spin"}):(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),P?"Saving...":"Save Changes"]})})]})]}),"language"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-bold mb-6",children:"Language & Region Settings"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"language",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Language"}),(0,a.jsxs)("select",{id:"language",value:language,onChange:e=>setLanguage(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:"english",children:"English"}),(0,a.jsx)("option",{value:"spanish",children:"Spanish"}),(0,a.jsx)("option",{value:"french",children:"French"}),(0,a.jsx)("option",{value:"german",children:"German"}),(0,a.jsx)("option",{value:"chinese",children:"Chinese"}),(0,a.jsx)("option",{value:"japanese",children:"Japanese"}),(0,a.jsx)("option",{value:"korean",children:"Korean"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"currency",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Currency"}),(0,a.jsxs)("select",{id:"currency",value:currency,onChange:e=>setCurrency(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:"usd",children:"USD - US Dollar"}),(0,a.jsx)("option",{value:"eur",children:"EUR - Euro"}),(0,a.jsx)("option",{value:"gbp",children:"GBP - British Pound"}),(0,a.jsx)("option",{value:"jpy",children:"JPY - Japanese Yen"}),(0,a.jsx)("option",{value:"cad",children:"CAD - Canadian Dollar"}),(0,a.jsx)("option",{value:"aud",children:"AUD - Australian Dollar"})]})]}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center",children:[(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]}),"payment"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-bold mb-6",children:"Payment Methods"}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center",children:[(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]}),"security"===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-bold mb-6",children:"Security Settings"}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center",children:[(0,a.jsx)(p,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]})})]})]}):(0,a.jsx)("div",{className:"p-4 flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-medium mb-2",children:"Please sign in to access settings"}),(0,a.jsx)("div",{className:"text-gray-500 mb-4",children:"You need to be authenticated to view and modify your settings."}),(0,a.jsx)("button",{onClick:()=>window.location.href="/login",className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90",children:"Go to Sign In"})]})})}},99891:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[340,658,817,452,895,887],()=>t(44254));module.exports=a})();
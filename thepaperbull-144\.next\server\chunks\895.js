exports.id=895,exports.ids=[895],exports.modules={3143:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},6955:(e,r,t)=>{"use strict";t.d(r,{A:()=>c,O:()=>o});var s=t(60687),i=t(43210);t(50227),t(80043);var a=t(95216);let n=(0,i.createContext)(void 0);function o({children:e}){let[r,t]=(0,i.useState)(null),[o,c]=(0,i.useState)(!0),u=async(e,r)=>{try{return await (0,a.Ru)(e,r)}catch(e){throw console.error("Sign in error:",e),e}},d=async()=>{try{await (0,a.signOutUser)()}catch(e){throw console.error("Sign out error:",e),e}},l=async()=>{try{return await (0,a.G6)()}catch(e){throw console.error("Google sign in error:",e),e}};return(0,s.jsx)(n.Provider,{value:{user:r,loading:o,signIn:u,signOut:d,signInWithGoogle:l},children:e})}function c(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},21643:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var s=t(83427),i=t(80043),a=t(50227),n=t(70695),o=t(38239);function c(e){return{id:e.id,email:e.email,name:e.name,createdAt:e.createdAt?.toMillis?.()||Date.now(),subscription:{type:e.subscription.type,status:e.subscription.status,startDate:e.subscription.startDate?.toMillis?.()||Date.now(),endDate:e.subscription.endDate?.toMillis?.(),features:e.subscription.features},balance:{current:e.balance.current,maximum:e.balance.maximum,currency:e.balance.currency,lastUpdated:e.balance.lastUpdated?.toMillis?.()||Date.now(),transactions:[]},settings:e.settings}}class u{constructor(){this.currentUser=null,this.firebaseUser=null,this.subscribers=[],this.unsubscribeAuth=null,this.unsubscribeProfile=null,this.transactions=[],this.initializeAuth()}initializeAuth(){this.unsubscribeAuth=(0,a.hg)(i.j2,async e=>{this.firebaseUser=e,e?await this.loadUserProfile(e.uid):(this.currentUser=null,this.transactions=[],this.unsubscribeProfile&&(this.unsubscribeProfile(),this.unsubscribeProfile=null),this.notifySubscribers())})}async loadUserProfile(e){try{let r=await s.b.getUserProfile(e);!r&&this.firebaseUser&&(r=await s.b.createUserProfile(e,this.firebaseUser.email||"",this.firebaseUser.displayName||"User"))&&await n.A.createWelcomeNotification(e),r&&(this.currentUser=c(r),this.transactions=await s.b.getUserTransactions(e),this.currentUser.balance.transactions=this.transactions.map(e=>({id:e.id,type:e.type,amount:e.amount,balance_before:e.balance_before,balance_after:e.balance_after,description:e.description,timestamp:e.timestamp?.toMillis?.()||Date.now()})),this.unsubscribeProfile&&this.unsubscribeProfile(),this.unsubscribeProfile=s.b.subscribeToUserProfile(e,e=>{e&&(this.currentUser=c(e),this.notifySubscribers())}),this.notifySubscribers())}catch(e){console.error("Error loading user profile:",e),"permission-denied"===e.code?(console.error("Permission denied - Firestore security rules may need to be updated"),this.firebaseUser&&(this.currentUser={id:this.firebaseUser.uid,email:this.firebaseUser.email||"",name:this.firebaseUser.displayName||"User",avatar:"bull-trader",balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:Date.now(),transactions:[]},subscription:{type:"free",status:"active"},settings:{},createdAt:Date.now(),lastLoginAt:Date.now()},this.notifySubscribers())):(this.currentUser=null,this.notifySubscribers())}}notifySubscribers(){this.subscribers.forEach(e=>e(this.currentUser))}getUser(){return this.currentUser}getUserBalance(){return this.currentUser?.balance?.current||1e4}getMaxBalance(){return this.currentUser?.balance.maximum||o.sC}getAvailableFunds(){return this.currentUser?this.currentUser.balance.maximum-this.currentUser.balance.current:0}canAddFunds(e){return this.currentUser?e<=0?{canAdd:!1,reason:"Amount must be positive"}:this.currentUser.balance.current+e>this.currentUser.balance.maximum?{canAdd:!1,reason:`Would exceed maximum balance of ${this.currentUser.balance.maximum.toLocaleString()} USDT`}:{canAdd:!0}:{canAdd:!1,reason:"User not found"}}async addFunds(e){if(!this.currentUser||!this.firebaseUser)return{success:!1,new_balance:0,requires_subscription:!1,message:"User not found"};let r=this.canAddFunds(e.amount);if(!r.canAdd){let t=this.currentUser.balance.current+e.amount<=o.ND.premium.maxBalance;return{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:t,message:r.reason||"Cannot add funds"}}try{let r=this.currentUser.balance.current,t=r+e.amount;return await s.b.updateUserBalance(this.firebaseUser.uid,t),await s.b.addTransaction(this.firebaseUser.uid,{type:"deposit",amount:e.amount,balance_before:r,balance_after:t,description:"subscription_upgrade"===e.method?"Funds added via subscription":"Virtual funds added"}),{success:!0,new_balance:t,requires_subscription:!1,message:`Successfully added ${e.amount.toLocaleString()} USDT to your account`}}catch(e){return console.error("Error adding funds:",e),{success:!1,new_balance:this.currentUser.balance.current,requires_subscription:!1,message:"Failed to add funds. Please try again."}}}async upgradeSubscription(e){if(!this.currentUser||!this.firebaseUser)return!1;try{let r=o.ND[e];return await s.b.updateUserProfile(this.firebaseUser.uid,{subscription:{type:e,status:"active",startDate:new Date,endDate:new Date(Date.now()+31536e6),features:r.features},balance:{...this.currentUser.balance,maximum:r.maxBalance}}),!0}catch(e){return console.error("Error upgrading subscription:",e),!1}}async updateBalance(e,r,t){if(this.currentUser&&this.firebaseUser)try{let i=this.currentUser.balance.current;await s.b.updateUserBalance(this.firebaseUser.uid,e),await s.b.addTransaction(this.firebaseUser.uid,{type:r,amount:e-i,balance_before:i,balance_after:e,description:t})}catch(e){console.error("Error updating balance:",e)}}subscribe(e){return this.subscribers.push(e),e(this.currentUser),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}getFirebaseUser(){return this.firebaseUser}destroy(){this.unsubscribeAuth&&this.unsubscribeAuth(),this.unsubscribeProfile&&this.unsubscribeProfile()}}let d=new u},25878:(e,r,t)=>{"use strict";t.d(r,{D:()=>c,N:()=>o});var s=t(60687),i=t(43210),a=t(29052);let n=(0,i.createContext)(void 0);function o({children:e}){let[r,t]=(0,i.useState)("light"),[o,c]=(0,i.useState)("emerald"),[u,d]=(0,i.useState)("light"),[l,h]=(0,i.useState)(!1);return l?(0,s.jsx)(n.Provider,{value:{theme:r,setTheme:e=>{t(e),a.A.updateTheme(e)},colorScheme:o,setColorScheme:e=>{c(e),a.A.updateColorScheme(e)},resolvedTheme:u},children:e}):(0,s.jsx)(s.Fragment,{children:e})}function c(){let e=(0,i.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},29052:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(83427),i=t(21643);class a{constructor(){this.preferences={favoriteMarkets:[],theme:"light",colorScheme:"emerald"},this.subscribers=[],this.isInitialized=!1,i.A.subscribe(e=>{e?this.loadUserPreferences(e.id):this.resetPreferences()})}subscribe(e){return this.subscribers.push(e),this.isInitialized&&e(this.preferences),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.preferences))}async loadUserPreferences(e){try{let r=await s.b.getUserProfile(e);r?.preferences&&(this.preferences={...this.preferences,...r.preferences}),this.isInitialized=!0,this.notifySubscribers()}catch(e){console.error("Error loading user preferences:",e),this.isInitialized=!0,this.notifySubscribers()}}resetPreferences(){this.preferences={favoriteMarkets:[],theme:"light",colorScheme:"emerald"},this.isInitialized=!1}getPreferences(){return{...this.preferences}}async updateFavoriteMarkets(e){let r=i.A.getFirebaseUser();if(r)try{this.preferences.favoriteMarkets=e,await s.b.updateUserProfile(r.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating favorite markets:",e)}}async updateTheme(e){let r=i.A.getFirebaseUser();if(!r){this.preferences.theme=e,this.notifySubscribers();return}try{this.preferences.theme=e,await s.b.updateUserProfile(r.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating theme:",e)}}async updateColorScheme(e){let r=i.A.getFirebaseUser();if(!r){this.preferences.colorScheme=e,this.notifySubscribers();return}try{this.preferences.colorScheme=e,await s.b.updateUserProfile(r.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating color scheme:",e)}}async updateRememberedUsername(e){try{e?this.preferences.rememberedUsername=e:delete this.preferences.rememberedUsername,this.notifySubscribers()}catch(e){console.error("Error updating remembered username:",e)}}getRememberedUsername(){return this.preferences.rememberedUsername?this.preferences.rememberedUsername:null}async toggleFavoriteMarket(e){let r=this.preferences.favoriteMarkets,t=r.includes(e)?r.filter(r=>r!==e):[...r,e];await this.updateFavoriteMarkets(t)}isFavoriteMarket(e){return this.preferences.favoriteMarkets.includes(e)}getFavoriteMarkets(){return[...this.preferences.favoriteMarkets]}getTheme(){return this.preferences.theme}getColorScheme(){return this.preferences.colorScheme}}let n=new a},38239:(e,r,t)=>{"use strict";t.d(r,{ND:()=>s,sC:()=>i});let s={free:{type:"free",name:"Free",maxBalance:5e4,price:0,features:{maxBalance:5e4,advancedAnalytics:!1,prioritySupport:!1,customIndicators:!1,apiAccess:!1}},premium:{type:"premium",name:"Premium",maxBalance:5e5,price:29.99,features:{maxBalance:5e5,advancedAnalytics:!0,prioritySupport:!1,customIndicators:!0,apiAccess:!1}},pro:{type:"pro",name:"Pro",maxBalance:1e6,price:99.99,features:{maxBalance:1e6,advancedAnalytics:!0,prioritySupport:!0,customIndicators:!0,apiAccess:!0}}},i=5e4},55183:(e,r,t)=>{"use strict";t.d(r,{E$:()=>g,NotificationContainer:()=>y});var s=t(60687),i=t(43210),a=t(5336),n=t(93613),o=t(43649),c=t(96882),u=t(11860),d=t(96241);let l={success:a.A,error:n.A,warning:o.A,info:c.A},h={success:"border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100",error:"border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100",warning:"border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100",info:"border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100"},b={"top-right":"top-4 right-4","top-left":"top-4 left-4","bottom-right":"bottom-4 right-4","bottom-left":"bottom-4 left-4","top-center":"top-4 left-1/2 transform -translate-x-1/2","bottom-center":"bottom-4 left-1/2 transform -translate-x-1/2"};function p({id:e,type:r="info",title:t,message:a,duration:n=4e3,onClose:o,closable:c=!0,position:p="top-right"}){let[f,m]=(0,i.useState)(!0),[g,y]=(0,i.useState)(!1),w=l[r];return f?(0,s.jsx)("div",{className:(0,d.cn)("fixed z-50 w-full max-w-sm p-4 border rounded-lg shadow-lg backdrop-blur-sm","transition-all duration-300 ease-in-out",!g&&"animate-slideInFromTop",g&&"animate-slideOutToRight",h[r],b[p]),style:{animation:g?"slideOutToRight 0.3s ease-in":"slideInFromTop 0.3s ease-out"},role:"alert","aria-live":"polite",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(w,{className:"h-5 w-5 mt-0.5 flex-shrink-0"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[t&&(0,s.jsx)("div",{className:"font-medium text-sm mb-1",children:t}),(0,s.jsx)("div",{className:"text-sm",children:a})]}),c&&(0,s.jsx)("button",{onClick:()=>{y(!0),setTimeout(()=>{m(!1),o?.()},300)},className:(0,d.cn)("flex-shrink-0 p-1 rounded-md transition-colors duration-200","hover:bg-black/10 dark:hover:bg-white/10","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current","text-current/70 hover:text-current"),"aria-label":"Close notification",children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})]})}):null}class f{generateId(){return`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}notify(){this.listeners.forEach(e=>e())}show(e){let r=this.generateId(),t={...e,id:r,onClose:()=>this.remove(r)};return this.notifications.set(r,t),this.notify(),r}remove(e){this.notifications.delete(e),this.notify()}clear(){this.notifications.clear(),this.notify()}getAll(){return Array.from(this.notifications.values())}subscribe(e){return this.listeners.add(e),()=>this.listeners.delete(e)}success(e,r){return this.show({...r,type:"success",message:e})}error(e,r){return this.show({...r,type:"error",message:e})}warning(e,r){return this.show({...r,type:"warning",message:e})}info(e,r){return this.show({...r,type:"info",message:e})}constructor(){this.notifications=new Map,this.listeners=new Set}}let m=new f;function g(){let[e,r]=(0,i.useState)([]);return{notifications:e,show:m.show.bind(m),remove:m.remove.bind(m),clear:m.clear.bind(m),success:m.success.bind(m),error:m.error.bind(m),warning:m.warning.bind(m),info:m.info.bind(m)}}function y(){let{notifications:e}=g();return(0,s.jsx)(s.Fragment,{children:e.map(e=>(0,s.jsx)(p,{...e},e.id))})}},58014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>u});var s=t(37413);t(82704);var i=t(61421),a=t.n(i),n=t(80363),o=t(83317),c=t(88639);let u={title:"ThePaperBull - Crypto Trading Platform",description:"Advanced cryptocurrency futures trading platform",generator:"v0.dev",metadataBase:new URL("https://thepaperbull.com"),openGraph:{title:"ThePaperBull - Advanced Crypto Trading Platform",description:"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.",images:[{url:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png",width:1200,height:630,alt:"ThePaperBull Trading Platform Interface"}],type:"website"},twitter:{card:"summary_large_image",title:"ThePaperBull - Advanced Crypto Trading Platform",description:"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.",images:["https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png"],creator:"@thepaperbull"}};function d({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:a().className,children:(0,s.jsxs)(c.Providers,{children:[e,(0,s.jsx)(n.Toaster,{}),(0,s.jsx)(o.NotificationContainer,{})]})})})}},70695:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(75535),i=t(80043);class a{initialize(e){this.currentUser=e,this.unsubscribeFirestore&&(this.unsubscribeFirestore(),this.unsubscribeFirestore=null),e?this.subscribeToUserNotifications(e.uid):(this.notifications=[],this.notifySubscribers())}subscribeToUserNotifications(e){let r=(0,s.rJ)(i.db,"notifications"),t=(0,s.P)(r,(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));this.unsubscribeFirestore=(0,s.aQ)(t,e=>{let r=e.docs.map(e=>({id:e.id,...e.data()})),t=this.notifications.filter(e=>e.id.startsWith("temp_"));this.notifications=[...t,...r],this.notifySubscribers()},e=>{console.error("Error listening to notifications:",e)})}subscribe(e){return this.subscribers.push(e),e(this.notifications),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.notifications))}getNotifications(){return this.notifications}getUnreadCount(){return this.notifications.filter(e=>!e.read).length}async markAsRead(e){if(this.currentUser)try{let r=(0,s.H9)(i.db,"notifications",e);await (0,s.mZ)(r,{read:!0})}catch(e){console.error("Error marking notification as read:",e)}}async markAllAsRead(){if(this.currentUser)try{let e=this.notifications.filter(e=>!e.read).map(e=>this.markAsRead(e.id));await Promise.all(e)}catch(e){console.error("Error marking all notifications as read:",e)}}async createNotification(e,r,t,a="info",n){try{let o={id:`temp_${Date.now()}`,userId:e,title:r,message:t,type:a,read:!1,createdAt:new Date,data:n||null};this.notifications.unshift(o),this.notifySubscribers();let c=(0,s.rJ)(i.db,"notifications"),u=await (0,s.gS)(c,{userId:e,title:r,message:t,type:a,read:!1,createdAt:(0,s.O5)(),data:n||null}),d=this.notifications.findIndex(e=>e.id===o.id);-1!==d&&(this.notifications[d].id=u.id)}catch(e){console.error("Error creating notification:",e)}}async createTradeNotification(e,r,t){let s="",i="";switch(r){case"order_filled":s="Order Filled",i=`Your ${t.side} order for ${t.symbol} has been filled at $${t.price}`;break;case"position_opened":s="Position Opened",i=`New ${t.side} position opened for ${t.symbol} - Size: ${t.size}`;break;case"position_closed":s="Position Closed",i=`${t.symbol} position closed - P&L: ${t.pnl>0?"+":""}$${t.pnl.toFixed(2)}`;break;case"stop_loss":s="Stop Loss Triggered",i=`Stop loss triggered for ${t.symbol} at $${t.price}`;break;case"take_profit":s="Take Profit Hit",i=`Take profit reached for ${t.symbol} at $${t.price}`}await this.createNotification(e,s,i,"trade",t)}async createWelcomeNotification(e){await this.createNotification(e,"Welcome to ThePaperBull!","Start your paper trading journey with $10,000 virtual balance. Practice trading without risk!","success")}destroy(){this.unsubscribeFirestore&&this.unsubscribeFirestore(),this.subscribers=[],this.notifications=[]}constructor(){this.subscribers=[],this.unsubscribeFirestore=null,this.currentUser=null,this.notifications=[]}}let n=new a},73033:(e,r,t)=>{Promise.resolve().then(t.bind(t,85969)),Promise.resolve().then(t.bind(t,55183)),Promise.resolve().then(t.bind(t,94593))},78335:()=>{},80043:(e,r,t)=>{"use strict";t.d(r,{db:()=>c,j2:()=>o});var s=t(67989),i=t(50227),a=t(75535);t(10178);let n=0===(0,s.Dk)().length?(0,s.Wp)({apiKey:"AIzaSyBYTl5SiY2ARvKUCiMBxf8zNUBQRu3hg1s",authDomain:"thepaperbull-144.firebaseapp.com",projectId:"thepaperbull-144",storageBucket:"thepaperbull-144.firebasestorage.app",messagingSenderId:"540770032311",appId:"1:540770032311:web:54b0d4ec1715779408cb32",measurementId:"G-5KTY505WKQ"}):(0,s.Dk)()[0],o=(0,i.xI)(n),c=(0,a.aU)(n)},80363:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\sonner.tsx","Toaster")},82704:()=>{},82761:(e,r,t)=>{Promise.resolve().then(t.bind(t,88639)),Promise.resolve().then(t.bind(t,83317)),Promise.resolve().then(t.bind(t,80363))},83317:(e,r,t)=>{"use strict";t.d(r,{NotificationContainer:()=>i});var s=t(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","Notification"),(0,s.registerClientReference)(function(){throw Error("Attempted to call notificationManager() from the server but notificationManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","notificationManager"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","useNotifications");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call NotificationContainer() from the server but NotificationContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx","NotificationContainer")},83427:(e,r,t)=>{"use strict";t.d(r,{b:()=>n});var s=t(75535),i=t(80043);class a{async createUserProfile(e,r,t){let a=(0,s.O5)(),n={email:r,name:t,avatar:"bull-trader",createdAt:a,subscription:{type:"free",status:"active",startDate:a,features:["basic_trading","paper_trading","basic_charts"]},balance:{current:1e4,maximum:5e4,currency:"USDT",lastUpdated:a},settings:{theme:"system",notifications:{email:!0,push:!0,trading_alerts:!0,price_alerts:!0,news_updates:!1},trading:{default_leverage:10,risk_management:!0,auto_close_positions:!1,confirmation_dialogs:!0}}};return await (0,s.BN)((0,s.H9)(i.db,"users",e),n),await this.addTransaction(e,{type:"deposit",amount:1e4,balance_before:0,balance_after:1e4,description:"Initial balance"}),{id:e,...n}}async getUserProfile(e){try{let r=await (0,s.x7)((0,s.H9)(i.db,"users",e));if(r.exists())return{id:e,...r.data()};return null}catch(e){return console.error("Error getting user profile:",e),null}}async updateUserProfile(e,r){try{let t={...r};r.balance&&(t["balance.lastUpdated"]=(0,s.O5)()),await (0,s.mZ)((0,s.H9)(i.db,"users",e),t)}catch(e){throw console.error("Error updating user profile:",e),e}}async updateUserBalance(e,r){try{await (0,s.mZ)((0,s.H9)(i.db,"users",e),{"balance.current":r,"balance.lastUpdated":(0,s.O5)()})}catch(e){throw console.error("Error updating user balance:",e),e}}async addTransaction(e,r){try{let t={...r,userId:e,timestamp:(0,s.O5)()};return(await (0,s.gS)((0,s.rJ)(i.db,"transactions"),t)).id}catch(e){throw console.error("Error adding transaction:",e),e}}async getUserTransactions(e,r=50){try{let t=(0,s.P)((0,s.rJ)(i.db,"transactions"),(0,s._M)("userId","==",e),(0,s.My)("timestamp","desc"));return new Promise((e,i)=>{(0,s.aQ)(t,t=>{let s=t.docs.map(e=>({id:e.id,...e.data()}));e(s.slice(0,r))},i)})}catch(e){return console.error("Error getting user transactions:",e),[]}}async addPosition(e,r){try{console.log("Adding position to Firestore:",{userId:e,position:r});let t=(0,s.O5)(),a={...r,userId:e,createdAt:t,updatedAt:t},n=await (0,s.gS)((0,s.rJ)(i.db,"positions"),a);return console.log("Position added successfully with ID:",n.id),n.id}catch(e){if(console.error("Error adding position to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");throw Error(`Failed to create position: ${e.message||"Unknown error"}`)}}async updatePosition(e,r){try{await (0,s.mZ)((0,s.H9)(i.db,"positions",e),{...r,updatedAt:(0,s.O5)()})}catch(e){throw console.error("Error updating position:",e),e}}async deletePosition(e){try{await (0,s.kd)((0,s.H9)(i.db,"positions",e))}catch(e){throw console.error("Error deleting position:",e),e}}async getUserPositions(e){try{let r=(0,s.P)((0,s.rJ)(i.db,"positions"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,t)=>{(0,s.aQ)(r,r=>{let t=r.docs.map(e=>({id:e.id,...e.data()}));e(t)},t)})}catch(e){return console.error("Error getting user positions:",e),[]}}async addOrder(e,r){try{console.log("Adding order to Firestore:",{userId:e,order:r});let t=(0,s.O5)(),a={...r,userId:e,createdAt:t,updatedAt:t},n=await (0,s.gS)((0,s.rJ)(i.db,"orders"),a);return console.log("Order added successfully with ID:",n.id),n.id}catch(e){if(console.error("Error adding order to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");if("failed-precondition"===e.code)throw Error("Database operation failed. Please check your connection and try again.");throw Error(`Failed to place order: ${e.message||"Unknown error"}`)}}async updateOrder(e,r){try{await (0,s.mZ)((0,s.H9)(i.db,"orders",e),{...r,updatedAt:(0,s.O5)()})}catch(e){throw console.error("Error updating order:",e),e}}async deleteOrder(e){try{await (0,s.kd)((0,s.H9)(i.db,"orders",e))}catch(e){throw console.error("Error deleting order:",e),e}}async getUserOrders(e){try{let r=(0,s.P)((0,s.rJ)(i.db,"orders"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,t)=>{(0,s.aQ)(r,r=>{let t=r.docs.map(e=>({id:e.id,...e.data()}));e(t)},t)})}catch(e){return console.error("Error getting user orders:",e),[]}}async addTrade(e,r){try{console.log("Adding trade to Firestore:",{userId:e,trade:r});let t={...r,userId:e,createdAt:(0,s.O5)()},a=await (0,s.gS)((0,s.rJ)(i.db,"trades"),t);return console.log("Trade added successfully with ID:",a.id),a.id}catch(e){if(console.error("Error adding trade to Firestore:",e),"permission-denied"===e.code)throw Error("Permission denied. Please check your authentication and try again.");if("unavailable"===e.code)throw Error("Service temporarily unavailable. Please try again in a moment.");throw Error(`Failed to record trade: ${e.message||"Unknown error"}`)}}async getUserTrades(e,r=100){try{let t=(0,s.P)((0,s.rJ)(i.db,"trades"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return new Promise((e,i)=>{let a=(0,s.aQ)(t,t=>{let s=t.docs.map(e=>({id:e.id,...e.data()}));a(),e(s.slice(0,r))},e=>{i(e)})})}catch(e){return console.error("Error getting user trades:",e),[]}}subscribeToUserProfile(e,r){return(0,s.aQ)((0,s.H9)(i.db,"users",e),t=>{t.exists()?r({id:e,...t.data()}):r(null)})}subscribeToUserPositions(e,r){let t=(0,s.P)((0,s.rJ)(i.db,"positions"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}subscribeToUserOrders(e,r){let t=(0,s.P)((0,s.rJ)(i.db,"orders"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}subscribeToUserTrades(e,r){let t=(0,s.P)((0,s.rJ)(i.db,"trades"),(0,s._M)("userId","==",e),(0,s.My)("createdAt","desc"));return(0,s.aQ)(t,e=>{r(e.docs.map(e=>({id:e.id,...e.data()})))})}}let n=new a},85969:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>n});var s=t(60687),i=t(6955),a=t(25878);function n({children:e}){return(0,s.jsx)(i.O,{children:(0,s.jsx)(a.N,{children:e})})}},88639:(e,r,t)=>{"use strict";t.d(r,{Providers:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\components\\providers.tsx","Providers")},94593:(e,r,t)=>{"use strict";t.d(r,{Toaster:()=>n});var s=t(60687),i=t(10218),a=t(52581);let n=({...e})=>{let{theme:r="system"}=(0,i.D)();return(0,s.jsx)(a.l$,{theme:r,className:"toaster group",closeButton:!0,duration:4e3,position:"top-right",expand:!0,richColors:!0,toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg group-[.toaster]:rounded-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",closeButton:"group-[.toast]:bg-background group-[.toast]:text-foreground group-[.toast]:border-border group-[.toast]:hover:bg-muted group-[.toast]:focus:ring-2 group-[.toast]:focus:ring-ring group-[.toast]:focus:ring-offset-2"}},...e})}},94895:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},95216:(e,r,t)=>{"use strict";t.d(r,{G6:()=>c,Ru:()=>o,bk:()=>n,signOutUser:()=>u});var s=t(50227),i=t(80043);let a=new s.HF;a.setCustomParameters({prompt:"select_account"});let n=async(e,r,t)=>{try{let a=await (0,s.eJ)(i.j2,e,r);return t&&a.user&&await (0,s.r7)(a.user,{displayName:t}),a.user}catch(e){throw console.error("Sign up error:",e),Error(d(e.code))}},o=async(e,r)=>{try{return(await (0,s.x9)(i.j2,e,r)).user}catch(e){throw console.error("Sign in error:",e),Error(d(e.code))}},c=async()=>{try{return(await (0,s.df)(i.j2,a)).user}catch(e){if(console.error("Google sign in error:",e),"auth/popup-closed-by-user"===e.code||"auth/cancelled-popup-request"===e.code)throw e;throw Error(d(e.code))}},u=async()=>{try{await (0,s.CI)(i.j2)}catch(e){throw console.error("Sign out error:",e),Error("Failed to sign out")}},d=e=>{switch(e){case"auth/user-not-found":return"No account found with this email address. Please check your email or sign up for a new account.";case"auth/wrong-password":return"Incorrect password. Please try again or reset your password.";case"auth/invalid-credential":return"Invalid email or password. Please check your credentials and try again.";case"auth/email-already-in-use":return"An account with this email already exists. Please sign in instead.";case"auth/weak-password":return"Password should be at least 6 characters long.";case"auth/invalid-email":return"Please enter a valid email address.";case"auth/user-disabled":return"This account has been disabled. Please contact support.";case"auth/too-many-requests":return"Too many failed attempts. Please try again later or reset your password.";case"auth/popup-closed-by-user":return"Sign-in popup was closed. Please try again.";case"auth/popup-blocked":return"Sign-in popup was blocked. Please allow popups and try again.";case"auth/network-request-failed":return"Network error. Please check your internet connection and try again.";case"auth/cancelled-popup-request":return"Sign-in was cancelled.";case"auth/account-exists-with-different-credential":return"An account already exists with the same email address but different sign-in credentials.";case"auth/operation-not-allowed":return"This sign-in method is not enabled. Please contact support.";case"auth/invalid-api-key":return"Invalid API key. Please check your Firebase configuration.";case"auth/app-deleted":return"Firebase app has been deleted. Please check your configuration.";case"auth/invalid-user-token":return"User token is invalid. Please sign in again.";case"auth/user-token-expired":return"User token has expired. Please sign in again.";default:return`Authentication error: ${e||"Unknown error"}`}}},96241:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var s=t(49384),i=t(82348);function a(...e){return(0,i.QP)((0,s.$)(e))}},96487:()=>{},99766:(e,r,t)=>{"use strict";function s(){return null}t.r(r),t.d(r,{default:()=>s})}};
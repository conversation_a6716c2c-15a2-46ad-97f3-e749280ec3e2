"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[135],{34135:(t,e,s)=>{s.d(e,{rM:()=>h,fx:()=>p});var i=s(95155),r=s(12115),a=s(91317),o=s(50475),n=s(27759);class l{subscribe(t){return this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(){this.subscribers.forEach(t=>t(this.state))}initializeUserSubscription(){this.userUnsubscribe=o.A.subscribe(t=>{t?(this.loadUserTradingData(t.id),this.initializeAccountData()):this.clearTradingData()})}async loadUserTradingData(t){try{this.state.isLoading=!0,this.notifySubscribers(),this.unsubscribePositions&&this.unsubscribePositions();try{this.unsubscribePositions=a.b.subscribeToUserPositions(t,t=>{let e=t.map(this.convertUserPositionToPosition),s=this.state.positions.filter(t=>t.id.startsWith("temp_"));this.state.positions=[...s,...e],this.updateAccountInfo(),this.notifySubscribers()})}catch(t){console.error("Error subscribing to positions:",t),this.state.positions=[]}this.unsubscribeOrders&&this.unsubscribeOrders();try{this.unsubscribeOrders=a.b.subscribeToUserOrders(t,t=>{this.state.orders=t.map(this.convertUserOrderToOrder),this.updateAccountInfo(),this.notifySubscribers()})}catch(t){console.error("Error subscribing to orders:",t),this.state.orders=[]}this.unsubscribeTrades&&this.unsubscribeTrades();try{this.unsubscribeTrades=a.b.subscribeToUserTrades(t,t=>{let e=t.map(this.convertUserTradeToTrade),s=this.state.trades.filter(t=>t.id.startsWith("temp_"));this.state.trades=[...s,...e],this.updateAccountInfo(),this.notifySubscribers()})}catch(t){console.error("Error subscribing to trades:",t),this.state.trades=[]}this.state.isLoading=!1,this.state.error=null,this.notifySubscribers()}catch(t){console.error("Error loading user trading data:",t),this.state.isLoading=!1,"permission-denied"===t.code?this.state.error="Trading data access requires proper authentication. Please sign in again.":this.state.error="Failed to load trading data. Please try again.",this.state.positions=[],this.state.orders=[],this.state.trades=[],this.notifySubscribers()}}clearTradingData(){this.state.positions=[],this.state.orders=[],this.state.trades=[],this.state.accountInfo=null,this.unsubscribePositions&&(this.unsubscribePositions(),this.unsubscribePositions=null),this.unsubscribeOrders&&(this.unsubscribeOrders(),this.unsubscribeOrders=null),this.unsubscribeTrades&&(this.unsubscribeTrades(),this.unsubscribeTrades=null),this.userServiceUnsubscribe&&(this.userServiceUnsubscribe(),this.userServiceUnsubscribe=null),this.notifySubscribers()}initializeAccountData(){this.userServiceUnsubscribe=o.A.subscribe(t=>{t&&this.syncAccountWithUserData(t)}),o.A.getUserBalance(),this.syncAccountWithUserData(o.A.getUser())}syncAccountWithUserData(t){var e;let s=(null==t?void 0:null===(e=t.balance)||void 0===e?void 0:e.current)||o.A.getUserBalance()||1e4,i=this.state.accountInfo,r=(null==i?void 0:i.totalUnrealizedProfit)||0,a=(null==i?void 0:i.totalPositionInitialMargin)||0,n=(null==i?void 0:i.totalOpenOrderInitialMargin)||0,l=s-a-n+r;this.state.accountInfo={totalWalletBalance:s,totalUnrealizedProfit:r,totalMarginBalance:s+r,totalPositionInitialMargin:a,totalOpenOrderInitialMargin:n,availableBalance:Math.max(0,l),maxWithdrawAmount:Math.max(0,l),balances:[{asset:"USDT",free:s,locked:0,total:s}],canTrade:!0,canDeposit:!0,canWithdraw:!0,updateTime:Date.now()},this.notifySubscribers()}updateAccountInfo(){if(!this.state.accountInfo)return;let t=this.state.positions.reduce((t,e)=>t+e.pnl,0),e=this.state.positions.reduce((t,e)=>t+e.margin,0),s=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>t+e.origQty*e.price/(e.leverage||10),0);this.state.accountInfo.totalUnrealizedProfit=t,this.state.accountInfo.totalPositionInitialMargin=e,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=this.state.accountInfo.totalWalletBalance+t;let i=this.state.accountInfo.totalWalletBalance-e-s+t;this.state.accountInfo.availableBalance=Math.max(0,i),this.state.accountInfo.maxWithdrawAmount=Math.max(0,i),this.state.accountInfo.updateTime=Date.now();let r=this.state.accountInfo.balances.find(t=>"USDT"===t.asset);r&&(r.free=this.state.accountInfo.availableBalance,r.locked=e+s,r.total=this.state.accountInfo.totalWalletBalance)}getPositions(){return[...this.state.positions]}getOrders(){return[...this.state.orders]}getTrades(){return[...this.state.trades]}getMarketData(t){return this.state.marketData[t]||null}getAccountInfo(){return this.state.accountInfo}updateMarketData(t,e){this.state.marketData[t]=e,this.state.positions=this.state.positions.map(s=>s.symbol===t?this.updatePositionPnL(s,e.price):s),this.updateAccountInfo(),this.notifySubscribers()}calculatePnL(t,e){let s=("LONG"===t.side?e-t.entryPrice:t.entryPrice-e)*t.size,i=s/t.margin*100;return{pnl:s,pnlPercent:i}}updatePositionPnL(t,e){let{pnl:s,pnlPercent:i}=this.calculatePnL(t,e);return{...t,markPrice:e,pnl:s,pnlPercent:i}}async placeOrder(t){let e=o.A.getFirebaseUser();if(console.log("Authentication check:",{hasUser:!!e,userId:null==e?void 0:e.uid,userEmail:null==e?void 0:e.email,userServiceUser:o.A.getUser()}),!e)throw console.error("Order placement failed: User not authenticated"),Error("User not authenticated. Please sign in and try again.");try{await e.getIdToken(!0),console.log("User token refreshed successfully")}catch(t){throw console.error("Token refresh failed:",t),Error("Authentication token expired. Please sign in again.")}if(!t.symbol||!t.side||!t.type||!t.quantity)throw console.error("Order placement failed: Invalid order parameters",t),Error("Invalid order parameters. Please check your order details.");if(t.quantity<=0)throw console.error("Order placement failed: Invalid quantity",t.quantity),Error("Order quantity must be greater than 0.");try{var s;this.state.isLoading=!0,this.notifySubscribers(),console.log("Placing order:",t);let i=null===(s=this.getMarketData(t.symbol))||void 0===s?void 0:s.price;!i&&t.price&&(i=t.price),i||(i=({BTCUSDT:43e3,ETHUSDT:2500,XRPUSDT:.6,SOLUSDT:100,BNBUSDT:300,DOGEUSDT:.08,ADAUSDT:.5,TRXUSDT:.1})[t.symbol]||100,console.warn("Using fallback price for ".concat(t.symbol,": ").concat(i)));let r=o.A.getUserBalance(),l=t.quantity*i,c=l/(t.leverage||10);if(c>r)throw console.error("Order placement failed: Insufficient balance",{requiredMargin:c,userBalance:r,orderValue:l}),Error("Insufficient balance. Required: ".concat(c.toFixed(2)," USDT, Available: ").concat(r.toFixed(2)," USDT"));let d={symbol:t.symbol,side:t.side,type:t.type,price:t.price||i,origQty:t.quantity,executedQty:0,status:"NEW",leverage:t.leverage||10,...t.stopLoss&&{stopLoss:t.stopLoss},...t.takeProfit&&{takeProfit:t.takeProfit}};console.log("Order data prepared:",d);let u=await a.b.addOrder(e.uid,d);return console.log("Order added to Firestore with ID:",u),"MARKET"===t.type?(console.log("Executing market order immediately"),await this.executeOrder(u,d,i)):(console.log("Limit order placed, waiting for execution"),n.A.createTradeNotification(e.uid,"order_placed",{symbol:t.symbol,side:t.side,type:t.type,price:t.price,quantity:t.quantity}).catch(t=>{console.error("Failed to create order placed notification:",t)})),this.state.isLoading=!1,this.state.error=null,this.notifySubscribers(),console.log("Order placement successful:",u),u}catch(t){throw console.error("Order placement error:",t),this.state.isLoading=!1,this.state.error=t instanceof Error?t.message:"Failed to place order",this.notifySubscribers(),t}}async executeOrder(t,e,s){let i=o.A.getFirebaseUser();if(!i)throw console.error("Cannot execute order: User not authenticated"),Error("User not authenticated");try{console.log("Executing order:",{orderId:t,orderData:e,executionPrice:s});let r=e.origQty*s*.001;console.log("Calculated commission:",r);let l={symbol:e.symbol,side:"BUY"===e.side?"LONG":"SHORT",entryPrice:s,markPrice:s,size:e.origQty,margin:e.origQty*s/e.leverage,leverage:e.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(s,"BUY"===e.side?"LONG":"SHORT",e.leverage),orderId:t,...e.stopLoss&&{stopLoss:e.stopLoss},...e.takeProfit&&{takeProfit:e.takeProfit}};console.log("Creating position:",l);let c="temp_".concat(Date.now()),d={id:c,...l,timestamp:Date.now()};this.state.positions.push(d);let u=this.state.orders.findIndex(e=>e.id===t);-1!==u&&(this.state.orders[u].status="FILLED",this.state.orders[u].executedQty=e.origQty),this.notifySubscribers(),console.log("Local state updated, now syncing with Firestore...");let[b]=await Promise.all([a.b.addPosition(i.uid,l),a.b.updateOrder(t,{status:"FILLED",executedQty:e.origQty})]);console.log("Firestore operations completed:",{positionId:b});let h=this.state.positions.findIndex(t=>t.id===c);-1!==h&&(this.state.positions[h].id=b),r>0&&(console.log("Updating user balance for commission"),o.A.updateBalance(o.A.getUserBalance()-r,"commission","Trading commission: ".concat(r.toFixed(2)," USDT")).catch(t=>{console.error("Failed to update balance:",t)})),n.A.createTradeNotification(i.uid,"position_opened",{symbol:e.symbol,side:l.side,size:e.origQty,entryPrice:s}).catch(t=>{console.error("Failed to create position opened notification:",t)}),console.log("Order execution completed successfully")}catch(e){console.error("Error executing order:",e);try{await a.b.updateOrder(t,{status:"FAILED"})}catch(t){console.error("Failed to update order status to FAILED:",t)}throw e}}calculateLiquidationPrice(t,e,s){let i=.995-1/s;return"LONG"===e?t*i:t/i}async cancelOrder(t){try{await a.b.updateOrder(t,{status:"CANCELLED"})}catch(t){throw console.error("Error cancelling order:",t),t}}async closePosition(t){let e=o.A.getFirebaseUser();if(e)try{var s;let i=this.state.positions.find(e=>e.id===t);if(!i)return;let r=(null===(s=this.getMarketData(i.symbol))||void 0===s?void 0:s.price)||i.markPrice;this.state.positions=this.state.positions.filter(e=>e.id!==t),this.notifySubscribers();let l={symbol:i.symbol,side:"LONG"===i.side?"SELL":"BUY",price:r,quantity:i.size,commission:i.size*r*.001,realizedPnl:i.pnl,leverage:i.leverage,orderId:i.orderId||"",positionId:t},c="temp_".concat(Date.now()),d={id:c,...l,timestamp:Date.now(),positionId:t};this.state.trades.unshift(d),this.notifySubscribers();let[u]=await Promise.all([a.b.addTrade(e.uid,l),a.b.deletePosition(t)]),b=this.state.trades.findIndex(t=>t.id===c);-1!==b&&(this.state.trades[b].id=u);let h=o.A.getUserBalance()+i.pnl-l.commission;o.A.updateBalance(h,i.pnl>0?"trade_profit":"trade_loss","Position closed: ".concat(i.pnl>0?"+":"").concat(i.pnl.toFixed(2)," USDT")).catch(t=>{console.error("Failed to update balance after closing position:",t)}),n.A.createTradeNotification(e.uid,"position_closed",{symbol:i.symbol,side:i.side,pnl:i.pnl,closePrice:r}).catch(t=>{console.error("Failed to create position closed notification:",t)})}catch(t){throw console.error("Error closing position:",t),t}}destroy(){this.unsubscribePositions&&this.unsubscribePositions(),this.unsubscribeOrders&&this.unsubscribeOrders(),this.unsubscribeTrades&&this.unsubscribeTrades(),this.userUnsubscribe&&this.userUnsubscribe(),this.userServiceUnsubscribe&&this.userServiceUnsubscribe()}constructor(){this.state={positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null},this.subscribers=[],this.unsubscribePositions=null,this.unsubscribeOrders=null,this.unsubscribeTrades=null,this.userUnsubscribe=null,this.userServiceUnsubscribe=null,this.orderIdCounter=1,this.positionIdCounter=1,this.tradeIdCounter=1,this.convertUserPositionToPosition=t=>{var e,s;return{id:t.id,symbol:t.symbol,side:t.side,entryPrice:t.entryPrice,markPrice:t.markPrice,size:t.size,margin:t.margin,leverage:t.leverage,pnl:t.pnl,pnlPercent:t.pnlPercent,liquidationPrice:t.liquidationPrice,stopLoss:t.stopLoss,takeProfit:t.takeProfit,timestamp:(null===(s=t.createdAt)||void 0===s?void 0:null===(e=s.toMillis)||void 0===e?void 0:e.call(s))||Date.now(),orderId:t.orderId}},this.convertUserOrderToOrder=t=>{var e,s;return{id:t.id,symbol:t.symbol,side:t.side,type:t.type,price:t.price,origQty:t.origQty,executedQty:t.executedQty,status:t.status,leverage:t.leverage,stopLoss:t.stopLoss,takeProfit:t.takeProfit,timestamp:(null===(s=t.createdAt)||void 0===s?void 0:null===(e=s.toMillis)||void 0===e?void 0:e.call(s))||Date.now()}},this.convertUserTradeToTrade=t=>{var e,s;return{id:t.id,symbol:t.symbol,side:t.side,price:t.price,quantity:t.quantity,commission:t.commission,realizedPnl:t.realizedPnl,leverage:t.leverage,timestamp:(null===(s=t.createdAt)||void 0===s?void 0:null===(e=s.toMillis)||void 0===e?void 0:e.call(s))||Date.now(),orderId:t.orderId,positionId:t.positionId}},this.initializeUserSubscription()}}let c=new l;class d{initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(t=>{this.simulators.set(t.symbol,{symbol:t.symbol,basePrice:t.basePrice,volatility:t.volatility,trend:(Math.random()-.5)*.001,lastPrice:t.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},1e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(t=>{let e=Date.now(),s=(e-t.lastUpdate)/1e3,i=(Math.random()-.5)*t.volatility*s,r=t.trend*s,a=t.lastPrice*(1+(i+r)),o=a-t.lastPrice,n=o/t.lastPrice*100;.01>Math.random()&&(t.trend=(Math.random()-.5)*.001),t.lastPrice=a,t.lastUpdate=e;let l={symbol:t.symbol,price:a,priceChange:o,priceChangePercent:n,volume:1e6*Math.random(),timestamp:e};this.notifySubscribers(t.symbol,l)})}subscribe(t){return this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(t,e){this.subscribers.forEach(s=>s(t,e))}getCurrentPrice(t){let e=this.simulators.get(t);return e?e.lastPrice:null}addSymbol(t,e){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.03;this.simulators.has(t)||this.simulators.set(t,{symbol:t,basePrice:e,volatility:s,trend:(Math.random()-.5)*.001,lastPrice:e,lastUpdate:Date.now()})}removeSymbol(t){this.simulators.delete(t)}constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}}let u=new d,b=(0,r.createContext)(void 0);function h(t){let{children:e}=t,[s,a]=(0,r.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null});(0,r.useEffect)(()=>c.subscribe(t=>{a(t)}),[]),(0,r.useEffect)(()=>{let t=u.subscribe((t,e)=>{c.updateMarketData(t,e)});return u.start(),()=>{t(),u.stop()}},[]);let o=(0,r.useCallback)(async t=>{try{return await c.placeOrder(t)}catch(t){throw console.error("Failed to place order:",t),t}},[]),n=(0,r.useCallback)(async t=>{try{return await c.cancelOrder(t),!0}catch(t){throw console.error("Failed to cancel order:",t),t}},[]),l=(0,r.useCallback)(async t=>{try{return await c.closePosition(t),!0}catch(t){throw console.error("Failed to close position:",t),t}},[]),d=(0,r.useCallback)(async t=>{try{return console.log("Position update not implemented in Firebase service yet:",t),!0}catch(t){throw console.error("Failed to update position:",t),t}},[]),h=(0,r.useCallback)((t,e)=>{c.updateMarketData(t,e)},[]),p=(0,r.useCallback)(t=>{console.log("Account info update not needed with Firebase service:",t)},[]),y=(0,r.useCallback)(()=>{a(t=>({...t,error:null}))},[]),m=(0,r.useCallback)(t=>c.getMarketData(t),[]),f=(0,r.useCallback)(t=>s.positions.find(e=>e.symbol===t)||null,[s.positions]),g=(0,r.useCallback)(()=>c.getAccountInfo(),[]),v=(0,r.useCallback)(()=>s.positions.reduce((t,e)=>t+e.pnl,0),[s.positions]),P=(0,r.useCallback)(()=>s.positions.reduce((t,e)=>t+e.margin,0),[s.positions]),U=(0,r.useCallback)(()=>{var t;return(null===(t=s.accountInfo)||void 0===t?void 0:t.availableBalance)||0},[s.accountInfo]),I={positions:s.positions,orders:s.orders,trades:s.trades,marketData:s.marketData,accountInfo:s.accountInfo,isLoading:s.isLoading,error:s.error,state:s,placeOrder:o,cancelOrder:n,closePosition:l,updatePosition:d,updateMarketData:h,updateAccountInfo:p,clearError:y,getMarketData:m,getPositionBySymbol:f,getAccountInfo:g,getTotalPnL:v,getTotalMargin:P,getAvailableBalance:U};return(0,i.jsx)(b.Provider,{value:I,children:e})}function p(){let t=(0,r.useContext)(b);if(void 0===t)throw Error("useTrading must be used within a TradingProvider");return t}}}]);
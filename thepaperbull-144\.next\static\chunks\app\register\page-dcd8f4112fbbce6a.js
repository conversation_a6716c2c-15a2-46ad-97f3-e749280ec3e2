(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{19946:(e,s,t)=>{"use strict";t.d(s,{A:()=>m});var r=t(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,s)=>{let{color:t="currentColor",size:a=24,strokeWidth:d=2,absoluteStrokeWidth:m,className:c="",children:i,iconNode:n,...u}=e;return(0,r.createElement)("svg",{ref:s,...o,width:a,height:a,stroke:t,strokeWidth:m?24*Number(d)/Number(a):d,className:l("lucide",c),...u},[...n.map(e=>{let[s,t]=e;return(0,r.createElement)(s,t)}),...Array.isArray(i)?i:[i]])}),m=(e,s)=>{let t=(0,r.forwardRef)((t,o)=>{let{className:m,...c}=t;return(0,r.createElement)(d,{ref:o,iconNode:s,className:l("lucide-".concat(a(e)),m),...c})});return t.displayName="".concat(e),t}},28692:(e,s,t)=>{Promise.resolve().then(t.bind(t,43337))},43337:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var r=t(95155),a=t(12115),l=t(35695),o=t(6874),d=t.n(o),m=t(78749),c=t(92657),i=t(5196),n=t(54416);let u=(0,t(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function x(){let e=(0,l.useRouter)(),[s,t]=(0,a.useState)({fullName:"",email:"",password:"",confirmPassword:""}),[o,x]=(0,a.useState)(!1),[h,p]=(0,a.useState)(!1),[f,w]=(0,a.useState)(!1),[g,y]=(0,a.useState)({}),[N,b]=(0,a.useState)(!1),j=[{id:"length",label:"At least 8 characters",test:e=>e.length>=8},{id:"uppercase",label:"At least 1 uppercase letter",test:e=>/[A-Z]/.test(e)},{id:"lowercase",label:"At least 1 lowercase letter",test:e=>/[a-z]/.test(e)},{id:"number",label:"At least 1 number",test:e=>/[0-9]/.test(e)},{id:"special",label:"At least 1 special character",test:e=>/[^A-Za-z0-9]/.test(e)}],v=e=>{let{name:s,value:r}=e.target;t(e=>({...e,[s]:r})),g[s]&&y(e=>({...e,[s]:""}))},k=()=>{let e={};return s.fullName.trim()||(e.fullName="Full name is required"),s.email?/\S+@\S+\.\S+/.test(s.email)||(e.email="Email is invalid"):e.email="Email is required",s.password?j.filter(e=>!e.test(s.password)).length>0&&(e.password="Password doesn't meet requirements"):e.password="Password is required",s.password!==s.confirmPassword&&(e.confirmPassword="Passwords do not match"),N||(e.terms="You must agree to the terms and conditions"),y(e),0===Object.keys(e).length};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"bg-emerald-100 rounded-full p-3",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-emerald-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z",clipRule:"evenodd"})})})}),(0,r.jsx)("h2",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Create an account"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Join our crypto trading platform"})]}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:t=>{t.preventDefault(),k()&&(w(!0),setTimeout(()=>{console.log("Registration data:",s),sessionStorage.setItem("registeredEmail",s.email),e.push("/login?registered=true")},1500))},children:[(0,r.jsxs)("div",{className:"rounded-md shadow-sm space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),(0,r.jsx)("input",{id:"fullName",name:"fullName",type:"text",autoComplete:"name",required:!0,value:s.fullName,onChange:v,className:"appearance-none relative block w-full px-3 py-2 border ".concat(g.fullName?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm"),placeholder:"John Doe"}),g.fullName&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.fullName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:s.email,onChange:v,className:"appearance-none relative block w-full px-3 py-2 border ".concat(g.email?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm"),placeholder:"<EMAIL>"}),g.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:"password",name:"password",type:o?"text":"password",autoComplete:"new-password",required:!0,value:s.password,onChange:v,className:"appearance-none relative block w-full px-3 py-2 border ".concat(g.password?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm pr-10"),placeholder:"••••••••"}),(0,r.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!o),children:o?(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(c.A,{className:"h-4 w-4 text-gray-400"})})]}),g.password&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.password})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Password requirements:"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:j.map(e=>(0,r.jsxs)("div",{className:"flex items-center text-xs ".concat(e.test(s.password)?"text-emerald-600":"text-gray-500"),children:[e.test(s.password)?(0,r.jsx)(i.A,{className:"h-3 w-3 mr-1"}):(0,r.jsx)(n.A,{className:"h-3 w-3 mr-1"}),e.label]},e.id))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:h?"text":"password",autoComplete:"new-password",required:!0,value:s.confirmPassword,onChange:v,className:"appearance-none relative block w-full px-3 py-2 border ".concat(g.confirmPassword?"border-red-300":"border-gray-300"," placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm pr-10"),placeholder:"••••••••"}),(0,r.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!h),children:h?(0,r.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}):(0,r.jsx)(c.A,{className:"h-4 w-4 text-gray-400"})})]}),g.confirmPassword&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.confirmPassword})]}),(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex items-center h-5",children:(0,r.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:N,onChange:()=>b(!N),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"})}),(0,r.jsx)("div",{className:"ml-3 text-sm",children:(0,r.jsxs)("label",{htmlFor:"terms",className:"font-medium text-gray-700",children:["I agree to the"," ",(0,r.jsx)("a",{href:"#",className:"text-emerald-600 hover:text-emerald-500",children:"Terms and Conditions"})," ","and"," ",(0,r.jsx)("a",{href:"#",className:"text-emerald-600 hover:text-emerald-500",children:"Privacy Policy"})]})})]}),g.terms&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g.terms})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:f,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 ".concat(f?"opacity-70 cursor-not-allowed":""),children:f?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Create Account"})})]}),(0,r.jsx)("div",{className:"text-center mt-4",children:(0,r.jsxs)(d(),{href:"/login",className:"inline-flex items-center font-medium text-emerald-600 hover:text-emerald-500",children:[(0,r.jsx)(u,{className:"h-4 w-4 mr-1"}),"Back to login"]})})]})})}},78749:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},92657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[914,441,684,358],()=>s(28692)),_N_E=e.O()}]);
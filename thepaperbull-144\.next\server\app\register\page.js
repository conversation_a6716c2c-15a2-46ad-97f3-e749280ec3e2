(()=>{var e={};e.id=454,e.ids=[454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14985:e=>{"use strict";e.exports=require("dns")},17647:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),a=r(43210),l=r(16189),i=r(85814),o=r.n(i),n=r(12597),d=r(13861),c=r(13964),m=r(11860);let p=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function u(){let e=(0,l.useRouter)(),[t,r]=(0,a.useState)({fullName:"",email:"",password:"",confirmPassword:""}),[i,u]=(0,a.useState)(!1),[x,h]=(0,a.useState)(!1),[f,g]=(0,a.useState)(!1),[y,b]=(0,a.useState)({}),[w,v]=(0,a.useState)(!1),j=[{id:"length",label:"At least 8 characters",test:e=>e.length>=8},{id:"uppercase",label:"At least 1 uppercase letter",test:e=>/[A-Z]/.test(e)},{id:"lowercase",label:"At least 1 lowercase letter",test:e=>/[a-z]/.test(e)},{id:"number",label:"At least 1 number",test:e=>/[0-9]/.test(e)},{id:"special",label:"At least 1 special character",test:e=>/[^A-Za-z0-9]/.test(e)}],N=e=>{let{name:t,value:s}=e.target;r(e=>({...e,[t]:s})),y[t]&&b(e=>({...e,[t]:""}))},P=()=>{let e={};return t.fullName.trim()||(e.fullName="Full name is required"),t.email?/\S+@\S+\.\S+/.test(t.email)||(e.email="Email is invalid"):e.email="Email is required",t.password?j.filter(e=>!e.test(t.password)).length>0&&(e.password="Password doesn't meet requirements"):e.password="Password is required",t.password!==t.confirmPassword&&(e.confirmPassword="Passwords do not match"),w||(e.terms="You must agree to the terms and conditions"),b(e),0===Object.keys(e).length};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-lg",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"bg-emerald-100 rounded-full p-3",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-emerald-600",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z",clipRule:"evenodd"})})})}),(0,s.jsx)("h2",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Create an account"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Join our crypto trading platform"})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:r=>{r.preventDefault(),P()&&(g(!0),setTimeout(()=>{console.log("Registration data:",t),sessionStorage.setItem("registeredEmail",t.email),e.push("/login?registered=true")},1500))},children:[(0,s.jsxs)("div",{className:"rounded-md shadow-sm space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"fullName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Full Name"}),(0,s.jsx)("input",{id:"fullName",name:"fullName",type:"text",autoComplete:"name",required:!0,value:t.fullName,onChange:N,className:`appearance-none relative block w-full px-3 py-2 border ${y.fullName?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm`,placeholder:"John Doe"}),y.fullName&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:y.fullName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:t.email,onChange:N,className:`appearance-none relative block w-full px-3 py-2 border ${y.email?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm`,placeholder:"<EMAIL>"}),y.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:y.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"password",name:"password",type:i?"text":"password",autoComplete:"new-password",required:!0,value:t.password,onChange:N,className:`appearance-none relative block w-full px-3 py-2 border ${y.password?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm pr-10`,placeholder:"••••••••"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>u(!i),children:i?(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(d.A,{className:"h-4 w-4 text-gray-400"})})]}),y.password&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:y.password})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Password requirements:"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:j.map(e=>(0,s.jsxs)("div",{className:`flex items-center text-xs ${e.test(t.password)?"text-emerald-600":"text-gray-500"}`,children:[e.test(t.password)?(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}):(0,s.jsx)(m.A,{className:"h-3 w-3 mr-1"}),e.label]},e.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:x?"text":"password",autoComplete:"new-password",required:!0,value:t.confirmPassword,onChange:N,className:`appearance-none relative block w-full px-3 py-2 border ${y.confirmPassword?"border-red-300":"border-gray-300"} placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm pr-10`,placeholder:"••••••••"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!x),children:x?(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-400"}):(0,s.jsx)(d.A,{className:"h-4 w-4 text-gray-400"})})]}),y.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:y.confirmPassword})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex items-center h-5",children:(0,s.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:w,onChange:()=>v(!w),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"})}),(0,s.jsx)("div",{className:"ml-3 text-sm",children:(0,s.jsxs)("label",{htmlFor:"terms",className:"font-medium text-gray-700",children:["I agree to the"," ",(0,s.jsx)("a",{href:"#",className:"text-emerald-600 hover:text-emerald-500",children:"Terms and Conditions"})," ","and"," ",(0,s.jsx)("a",{href:"#",className:"text-emerald-600 hover:text-emerald-500",children:"Privacy Policy"})]})})]}),y.terms&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:y.terms})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:f,className:`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 ${f?"opacity-70 cursor-not-allowed":""}`,children:f?(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):"Create Account"})})]}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)(o(),{href:"/login",className:"inline-flex items-center font-medium text-emerald-600 hover:text-emerald-500",children:[(0,s.jsx)(p,{className:"h-4 w-4 mr-1"}),"Back to login"]})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37468:(e,t,r)=>{Promise.resolve().then(r.bind(r,66493))},38004:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l}),r(37413);var s=r(31658);let a={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function l(e){let{__metadata_id__:t,...r}=await e.params,l=(0,s.fillMetadataSegment)(".",r,"opengraph-image"),{generateImageMetadata:i}=a;function o(e,t){let r={alt:e.alt,type:e.contentType||"image/png",url:l+(t?"/"+t:"")+"?53e13af1384a38e6"},{size:s}=e;return s&&(r.width=s.width,r.height=s.height),r}return i?(await i({params:r})).map((e,t)=>{let r=(e.id||t)+"";return o(e,r)}):[o(a,"")]}},55511:e=>{"use strict";e.exports=require("crypto")},59226:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),o=r(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66493)),"D:\\step-by-step\\thepaperbull-144\\app\\register\\page.tsx"]}]},{metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,75870))).default(e)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"D:\\step-by-step\\thepaperbull-144\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,99766)),"D:\\step-by-step\\thepaperbull-144\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,38004))).default(e)],twitter:[async e=>(await Promise.resolve().then(r.bind(r,75870))).default(e)],manifest:void 0}}]}.children,c=["D:\\step-by-step\\thepaperbull-144\\app\\register\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\step-by-step\\thepaperbull-144\\app\\register\\page.tsx","default")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},75870:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l}),r(37413);var s=r(31658);let a={runtime:"edge",dynamic:"force-static",alt:"Cryptocurrency Trading Dashboard",size:{width:1200,height:630},contentType:"image/png"};async function l(e){let{__metadata_id__:t,...r}=await e.params,l=(0,s.fillMetadataSegment)(".",r,"twitter-image"),{generateImageMetadata:i}=a;function o(e,t){let r={alt:e.alt,type:e.contentType||"image/png",url:l+(t?"/"+t:"")+"?c783d68783a9fc8b"},{size:s}=e;return s&&(r.width=s.width,r.height=s.height),r}return i?(await i({params:r})).map((e,t)=>{let r=(e.id||t)+"";return o(e,r)}):[o(a,"")]}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95612:(e,t,r)=>{Promise.resolve().then(r.bind(r,17647))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[340,658,817,895],()=>r(59226));module.exports=s})();
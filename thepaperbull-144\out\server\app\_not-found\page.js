/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"381f30465872\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxzdGVwLWJ5LXN0ZXBcXHRoZXBhcGVyYnVsbC0xNDRcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzODFmMzA0NjU4NzJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./components/ui/sonner.tsx\");\n/* harmony import */ var _components_ui_notification__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/notification */ \"(rsc)/./components/ui/notification.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./components/providers.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"ThePaperBull - Crypto Trading Platform\",\n    description: \"Advanced cryptocurrency futures trading platform\",\n    generator: \"v0.dev\",\n    metadataBase: new URL(\"https://thepaperbull.com\"),\n    openGraph: {\n        title: \"ThePaperBull - Advanced Crypto Trading Platform\",\n        description: \"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.\",\n        images: [\n            {\n                url: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png\",\n                width: 1200,\n                height: 630,\n                alt: \"ThePaperBull Trading Platform Interface\"\n            }\n        ],\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"ThePaperBull - Advanced Crypto Trading Platform\",\n        description: \"Trade cryptocurrency futures with advanced charting, real-time data, and powerful trading tools.\",\n        images: [\n            \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Screenshot%202025-04-21%20at%2000.01.09-1DI8PlTigyTXyu5nPuXVjFdbqYc5Av.png\"\n        ],\n        creator: \"@thepaperbull\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_4__.Providers, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_notification__WEBPACK_IMPORTED_MODULE_3__.NotificationContainer, {}, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\nfunction Loading() {\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbG9hZGluZy50c3giLCJtYXBwaW5ncyI6Ijs7OztBQUFlLFNBQVNBO0lBQ3RCLE9BQU87QUFDVCIsInNvdXJjZXMiOlsiRDpcXHN0ZXAtYnktc3RlcFxcdGhlcGFwZXJidWxsLTE0NFxcYXBwXFxsb2FkaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbIkxvYWRpbmciXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ Providers)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\providers.tsx",
"Providers",
);

/***/ }),

/***/ "(rsc)/./components/ui/notification.tsx":
/*!****************************************!*\
  !*** ./components/ui/notification.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Notification: () => (/* binding */ Notification),
/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer),
/* harmony export */   notificationManager: () => (/* binding */ notificationManager),
/* harmony export */   useNotifications: () => (/* binding */ useNotifications)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Notification = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Notification() from the server but Notification is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"Notification",
);const notificationManager = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call notificationManager() from the server but notificationManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"notificationManager",
);const useNotifications = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useNotifications() from the server but useNotifications is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"useNotifications",
);const NotificationContainer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NotificationContainer() from the server but NotificationContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\notification.tsx",
"NotificationContainer",
);

/***/ }),

/***/ "(rsc)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Toaster = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\step-by-step\\thepaperbull-144\\components\\ui\\sonner.tsx",
"Toaster",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\layout.tsx\"],\n'loading': [module2, \"D:\\\\step-by-step\\\\thepaperbull-144\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(rsc)/./components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/notification.tsx */ \"(rsc)/./components/ui/notification.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(rsc)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./components/providers.tsx":
/*!**********************************!*\
  !*** ./components/providers.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(ssr)/./contexts/auth-context.tsx\");\n/* harmony import */ var _contexts_theme_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/theme-context */ \"(ssr)/./contexts/theme-context.tsx\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_theme_context__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\providers.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\providers.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXNEO0FBQ0U7QUFNakQsU0FBU0UsVUFBVSxFQUFFQyxRQUFRLEVBQWtCO0lBQ3BELHFCQUNFLDhEQUFDSCxnRUFBWUE7a0JBQ1gsNEVBQUNDLGtFQUFhQTtzQkFDWEU7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxzdGVwLWJ5LXN0ZXBcXHRoZXBhcGVyYnVsbC0xNDRcXGNvbXBvbmVudHNcXHByb3ZpZGVycy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2NvbnRleHRzL2F1dGgtY29udGV4dFwiXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSBcIkAvY29udGV4dHMvdGhlbWUtY29udGV4dFwiXG5cbmludGVyZmFjZSBQcm92aWRlcnNQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFByb3ZpZGVycyh7IGNoaWxkcmVuIH06IFByb3ZpZGVyc1Byb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPEF1dGhQcm92aWRlcj5cbiAgICAgIDxUaGVtZVByb3ZpZGVyPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1RoZW1lUHJvdmlkZXI+XG4gICAgPC9BdXRoUHJvdmlkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJBdXRoUHJvdmlkZXIiLCJUaGVtZVByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/notification.tsx":
/*!****************************************!*\
  !*** ./components/ui/notification.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Notification: () => (/* binding */ Notification),\n/* harmony export */   NotificationContainer: () => (/* binding */ NotificationContainer),\n/* harmony export */   notificationManager: () => (/* binding */ notificationManager),\n/* harmony export */   useNotifications: () => (/* binding */ useNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,CheckCircle,Info,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Notification,notificationManager,useNotifications,NotificationContainer auto */ \n\n\n\nconst notificationIcons = {\n    success: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n    error: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    warning: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    info: _barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n};\nconst notificationStyles = {\n    success: \"border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100\",\n    error: \"border-red-200 bg-red-50 text-red-900 dark:border-red-800 dark:bg-red-950 dark:text-red-100\",\n    warning: \"border-yellow-200 bg-yellow-50 text-yellow-900 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-100\",\n    info: \"border-blue-200 bg-blue-50 text-blue-900 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-100\"\n};\nconst positionStyles = {\n    \"top-right\": \"top-4 right-4\",\n    \"top-left\": \"top-4 left-4\",\n    \"bottom-right\": \"bottom-4 right-4\",\n    \"bottom-left\": \"bottom-4 left-4\",\n    \"top-center\": \"top-4 left-1/2 transform -translate-x-1/2\",\n    \"bottom-center\": \"bottom-4 left-1/2 transform -translate-x-1/2\"\n};\nfunction Notification({ id, type = \"info\", title, message, duration = 4000, onClose, closable = true, position = \"top-right\" }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLeaving, setIsLeaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const Icon = notificationIcons[type];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Notification.useEffect\": ()=>{\n            if (duration > 0) {\n                const timer = setTimeout({\n                    \"Notification.useEffect.timer\": ()=>{\n                        handleClose();\n                    }\n                }[\"Notification.useEffect.timer\"], duration);\n                return ({\n                    \"Notification.useEffect\": ()=>clearTimeout(timer)\n                })[\"Notification.useEffect\"];\n            }\n        }\n    }[\"Notification.useEffect\"], [\n        duration\n    ]);\n    const handleClose = ()=>{\n        setIsLeaving(true);\n        setTimeout(()=>{\n            setIsVisible(false);\n            onClose?.();\n        }, 300) // Animation duration\n        ;\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed z-50 w-full max-w-sm p-4 border rounded-lg shadow-lg backdrop-blur-sm\", \"transition-all duration-300 ease-in-out\", !isLeaving && \"animate-slideInFromTop\", isLeaving && \"animate-slideOutToRight\", notificationStyles[type], positionStyles[position]),\n        style: {\n            animation: !isLeaving ? \"slideInFromTop 0.3s ease-out\" : \"slideOutToRight 0.3s ease-in\"\n        },\n        role: \"alert\",\n        \"aria-live\": \"polite\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-5 w-5 mt-0.5 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm mb-1\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: message\n                        }, void 0, false, {\n                            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                closable && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClose,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-shrink-0 p-1 rounded-md transition-colors duration-200\", \"hover:bg-black/10 dark:hover:bg-white/10\", \"focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-current\", \"text-current/70 hover:text-current\"),\n                    \"aria-label\": \"Close notification\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n// Notification Manager for programmatic usage\nclass NotificationManager {\n    generateId() {\n        return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    }\n    notify() {\n        this.listeners.forEach((listener)=>listener());\n    }\n    show(notification) {\n        const id = this.generateId();\n        const notificationWithId = {\n            ...notification,\n            id,\n            onClose: ()=>this.remove(id)\n        };\n        this.notifications.set(id, notificationWithId);\n        this.notify();\n        return id;\n    }\n    remove(id) {\n        this.notifications.delete(id);\n        this.notify();\n    }\n    clear() {\n        this.notifications.clear();\n        this.notify();\n    }\n    getAll() {\n        return Array.from(this.notifications.values());\n    }\n    subscribe(listener) {\n        this.listeners.add(listener);\n        return ()=>this.listeners.delete(listener);\n    }\n    // Convenience methods\n    success(message, options) {\n        return this.show({\n            ...options,\n            type: \"success\",\n            message\n        });\n    }\n    error(message, options) {\n        return this.show({\n            ...options,\n            type: \"error\",\n            message\n        });\n    }\n    warning(message, options) {\n        return this.show({\n            ...options,\n            type: \"warning\",\n            message\n        });\n    }\n    info(message, options) {\n        return this.show({\n            ...options,\n            type: \"info\",\n            message\n        });\n    }\n    constructor(){\n        this.notifications = new Map();\n        this.listeners = new Set();\n    }\n}\nconst notificationManager = new NotificationManager();\n// Hook for using notifications in React components\nfunction useNotifications() {\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useNotifications.useEffect\": ()=>{\n            const updateNotifications = {\n                \"useNotifications.useEffect.updateNotifications\": ()=>{\n                    setNotifications(notificationManager.getAll());\n                }\n            }[\"useNotifications.useEffect.updateNotifications\"];\n            updateNotifications();\n            const unsubscribe = notificationManager.subscribe(updateNotifications);\n            return unsubscribe;\n        }\n    }[\"useNotifications.useEffect\"], []);\n    return {\n        notifications,\n        show: notificationManager.show.bind(notificationManager),\n        remove: notificationManager.remove.bind(notificationManager),\n        clear: notificationManager.clear.bind(notificationManager),\n        success: notificationManager.success.bind(notificationManager),\n        error: notificationManager.error.bind(notificationManager),\n        warning: notificationManager.warning.bind(notificationManager),\n        info: notificationManager.info.bind(notificationManager)\n    };\n}\n// Notification Container Component\nfunction NotificationContainer() {\n    const { notifications } = useNotifications();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Notification, {\n                ...notification\n            }, notification.id, false, {\n                fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\notification.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this))\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL25vdGlmaWNhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ29DO0FBQy9DO0FBYWhDLE1BQU1RLG9CQUFvQjtJQUN4QkMsU0FBU04sd0hBQVdBO0lBQ3BCTyxPQUFPTix3SEFBV0E7SUFDbEJPLFNBQVNMLHdIQUFhQTtJQUN0Qk0sTUFBTVAsd0hBQUlBO0FBQ1o7QUFFQSxNQUFNUSxxQkFBcUI7SUFDekJKLFNBQVM7SUFDVEMsT0FBTztJQUNQQyxTQUFTO0lBQ1RDLE1BQU07QUFDUjtBQUVBLE1BQU1FLGlCQUFpQjtJQUNyQixhQUFhO0lBQ2IsWUFBWTtJQUNaLGdCQUFnQjtJQUNoQixlQUFlO0lBQ2YsY0FBYztJQUNkLGlCQUFpQjtBQUNuQjtBQUVPLFNBQVNDLGFBQWEsRUFDM0JDLEVBQUUsRUFDRkMsT0FBTyxNQUFNLEVBQ2JDLEtBQUssRUFDTEMsT0FBTyxFQUNQQyxXQUFXLElBQUksRUFDZkMsT0FBTyxFQUNQQyxXQUFXLElBQUksRUFDZkMsV0FBVyxXQUFXLEVBQ0o7SUFDbEIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUMwQixXQUFXQyxhQUFhLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUUzQyxNQUFNNEIsT0FBT3BCLGlCQUFpQixDQUFDUyxLQUFLO0lBRXBDaEIsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSW1CLFdBQVcsR0FBRztnQkFDaEIsTUFBTVMsUUFBUUM7b0RBQVc7d0JBQ3ZCQztvQkFDRjttREFBR1g7Z0JBRUg7OENBQU8sSUFBTVksYUFBYUg7O1lBQzVCO1FBQ0Y7aUNBQUc7UUFBQ1Q7S0FBUztJQUViLE1BQU1XLGNBQWM7UUFDbEJKLGFBQWE7UUFDYkcsV0FBVztZQUNUTCxhQUFhO1lBQ2JKO1FBQ0YsR0FBRyxLQUFLLHFCQUFxQjs7SUFDL0I7SUFFQSxJQUFJLENBQUNHLFdBQVcsT0FBTztJQUV2QixxQkFDRSw4REFBQ1M7UUFDQ0MsV0FBVzNCLDhDQUFFQSxDQUNYLCtFQUNBLDJDQUNBLENBQUNtQixhQUFhLDBCQUNkQSxhQUFhLDJCQUNiYixrQkFBa0IsQ0FBQ0ksS0FBSyxFQUN4QkgsY0FBYyxDQUFDUyxTQUFTO1FBRTFCWSxPQUFPO1lBQ0xDLFdBQVcsQ0FBQ1YsWUFDUixpQ0FDQTtRQUNOO1FBQ0FXLE1BQUs7UUFDTEMsYUFBVTtrQkFFViw0RUFBQ0w7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNOO29CQUFLTSxXQUFVOzs7Ozs7OEJBRWhCLDhEQUFDRDtvQkFBSUMsV0FBVTs7d0JBQ1poQix1QkFDQyw4REFBQ2U7NEJBQUlDLFdBQVU7c0NBQTRCaEI7Ozs7OztzQ0FFN0MsOERBQUNlOzRCQUFJQyxXQUFVO3NDQUFXZjs7Ozs7Ozs7Ozs7O2dCQUczQkcsMEJBQ0MsOERBQUNpQjtvQkFDQ0MsU0FBU1Q7b0JBQ1RHLFdBQVczQiw4Q0FBRUEsQ0FDWCwrREFDQSw0Q0FDQSwwRUFDQTtvQkFFRmtDLGNBQVc7OEJBRVgsNEVBQUN2Qyx3SEFBQ0E7d0JBQUNnQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXpCO0FBRUEsOENBQThDO0FBQzlDLE1BQU1RO0lBSUlDLGFBQXFCO1FBQzNCLE9BQU8sQ0FBQyxhQUFhLEVBQUVDLEtBQUtDLEdBQUcsR0FBRyxDQUFDLEVBQUVDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLElBQUk7SUFDaEY7SUFFUUMsU0FBUztRQUNmLElBQUksQ0FBQ0MsU0FBUyxDQUFDQyxPQUFPLENBQUNDLENBQUFBLFdBQVlBO0lBQ3JDO0lBRUFDLEtBQUtDLFlBQXVELEVBQVU7UUFDcEUsTUFBTXZDLEtBQUssSUFBSSxDQUFDMkIsVUFBVTtRQUMxQixNQUFNYSxxQkFBd0M7WUFDNUMsR0FBR0QsWUFBWTtZQUNmdkM7WUFDQUssU0FBUyxJQUFNLElBQUksQ0FBQ29DLE1BQU0sQ0FBQ3pDO1FBQzdCO1FBRUEsSUFBSSxDQUFDMEMsYUFBYSxDQUFDQyxHQUFHLENBQUMzQyxJQUFJd0M7UUFDM0IsSUFBSSxDQUFDTixNQUFNO1FBRVgsT0FBT2xDO0lBQ1Q7SUFFQXlDLE9BQU96QyxFQUFVLEVBQVE7UUFDdkIsSUFBSSxDQUFDMEMsYUFBYSxDQUFDRSxNQUFNLENBQUM1QztRQUMxQixJQUFJLENBQUNrQyxNQUFNO0lBQ2I7SUFFQVcsUUFBYztRQUNaLElBQUksQ0FBQ0gsYUFBYSxDQUFDRyxLQUFLO1FBQ3hCLElBQUksQ0FBQ1gsTUFBTTtJQUNiO0lBRUFZLFNBQThCO1FBQzVCLE9BQU9DLE1BQU1DLElBQUksQ0FBQyxJQUFJLENBQUNOLGFBQWEsQ0FBQ08sTUFBTTtJQUM3QztJQUVBQyxVQUFVYixRQUFvQixFQUFjO1FBQzFDLElBQUksQ0FBQ0YsU0FBUyxDQUFDZ0IsR0FBRyxDQUFDZDtRQUNuQixPQUFPLElBQU0sSUFBSSxDQUFDRixTQUFTLENBQUNTLE1BQU0sQ0FBQ1A7SUFDckM7SUFFQSxzQkFBc0I7SUFDdEI1QyxRQUFRVSxPQUFlLEVBQUVpRCxPQUFvQyxFQUFVO1FBQ3JFLE9BQU8sSUFBSSxDQUFDZCxJQUFJLENBQUM7WUFBRSxHQUFHYyxPQUFPO1lBQUVuRCxNQUFNO1lBQVdFO1FBQVE7SUFDMUQ7SUFFQVQsTUFBTVMsT0FBZSxFQUFFaUQsT0FBb0MsRUFBVTtRQUNuRSxPQUFPLElBQUksQ0FBQ2QsSUFBSSxDQUFDO1lBQUUsR0FBR2MsT0FBTztZQUFFbkQsTUFBTTtZQUFTRTtRQUFRO0lBQ3hEO0lBRUFSLFFBQVFRLE9BQWUsRUFBRWlELE9BQW9DLEVBQVU7UUFDckUsT0FBTyxJQUFJLENBQUNkLElBQUksQ0FBQztZQUFFLEdBQUdjLE9BQU87WUFBRW5ELE1BQU07WUFBV0U7UUFBUTtJQUMxRDtJQUVBUCxLQUFLTyxPQUFlLEVBQUVpRCxPQUFvQyxFQUFVO1FBQ2xFLE9BQU8sSUFBSSxDQUFDZCxJQUFJLENBQUM7WUFBRSxHQUFHYyxPQUFPO1lBQUVuRCxNQUFNO1lBQVFFO1FBQVE7SUFDdkQ7O2FBM0RRdUMsZ0JBQWdELElBQUlXO2FBQ3BEbEIsWUFBNkIsSUFBSW1COztBQTJEM0M7QUFFTyxNQUFNQyxzQkFBc0IsSUFBSTdCLHNCQUFxQjtBQUU1RCxtREFBbUQ7QUFDNUMsU0FBUzhCO0lBQ2QsTUFBTSxDQUFDZCxlQUFlZSxpQkFBaUIsR0FBR3pFLCtDQUFRQSxDQUFzQixFQUFFO0lBRTFFQyxnREFBU0E7c0NBQUM7WUFDUixNQUFNeUU7a0VBQXNCO29CQUMxQkQsaUJBQWlCRixvQkFBb0JULE1BQU07Z0JBQzdDOztZQUVBWTtZQUNBLE1BQU1DLGNBQWNKLG9CQUFvQkwsU0FBUyxDQUFDUTtZQUVsRCxPQUFPQztRQUNUO3FDQUFHLEVBQUU7SUFFTCxPQUFPO1FBQ0xqQjtRQUNBSixNQUFNaUIsb0JBQW9CakIsSUFBSSxDQUFDc0IsSUFBSSxDQUFDTDtRQUNwQ2QsUUFBUWMsb0JBQW9CZCxNQUFNLENBQUNtQixJQUFJLENBQUNMO1FBQ3hDVixPQUFPVSxvQkFBb0JWLEtBQUssQ0FBQ2UsSUFBSSxDQUFDTDtRQUN0QzlELFNBQVM4RCxvQkFBb0I5RCxPQUFPLENBQUNtRSxJQUFJLENBQUNMO1FBQzFDN0QsT0FBTzZELG9CQUFvQjdELEtBQUssQ0FBQ2tFLElBQUksQ0FBQ0w7UUFDdEM1RCxTQUFTNEQsb0JBQW9CNUQsT0FBTyxDQUFDaUUsSUFBSSxDQUFDTDtRQUMxQzNELE1BQU0yRCxvQkFBb0IzRCxJQUFJLENBQUNnRSxJQUFJLENBQUNMO0lBQ3RDO0FBQ0Y7QUFFQSxtQ0FBbUM7QUFDNUIsU0FBU007SUFDZCxNQUFNLEVBQUVuQixhQUFhLEVBQUUsR0FBR2M7SUFFMUIscUJBQ0U7a0JBQ0dkLGNBQWNvQixHQUFHLENBQUMsQ0FBQ3ZCLDZCQUNsQiw4REFBQ3hDO2dCQUFvQyxHQUFHd0MsWUFBWTtlQUFqQ0EsYUFBYXZDLEVBQUU7Ozs7OztBQUkxQyIsInNvdXJjZXMiOlsiRDpcXHN0ZXAtYnktc3RlcFxcdGhlcGFwZXJidWxsLTE0NFxcY29tcG9uZW50c1xcdWlcXG5vdGlmaWNhdGlvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBYLCBDaGVja0NpcmNsZSwgQWxlcnRDaXJjbGUsIEluZm8sIEFsZXJ0VHJpYW5nbGUgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBOb3RpZmljYXRpb25Qcm9wcyB7XG4gIGlkPzogc3RyaW5nXG4gIHR5cGU/OiBcInN1Y2Nlc3NcIiB8IFwiZXJyb3JcIiB8IFwid2FybmluZ1wiIHwgXCJpbmZvXCJcbiAgdGl0bGU/OiBzdHJpbmdcbiAgbWVzc2FnZTogc3RyaW5nXG4gIGR1cmF0aW9uPzogbnVtYmVyXG4gIG9uQ2xvc2U/OiAoKSA9PiB2b2lkXG4gIGNsb3NhYmxlPzogYm9vbGVhblxuICBwb3NpdGlvbj86IFwidG9wLXJpZ2h0XCIgfCBcInRvcC1sZWZ0XCIgfCBcImJvdHRvbS1yaWdodFwiIHwgXCJib3R0b20tbGVmdFwiIHwgXCJ0b3AtY2VudGVyXCIgfCBcImJvdHRvbS1jZW50ZXJcIlxufVxuXG5jb25zdCBub3RpZmljYXRpb25JY29ucyA9IHtcbiAgc3VjY2VzczogQ2hlY2tDaXJjbGUsXG4gIGVycm9yOiBBbGVydENpcmNsZSxcbiAgd2FybmluZzogQWxlcnRUcmlhbmdsZSxcbiAgaW5mbzogSW5mbyxcbn1cblxuY29uc3Qgbm90aWZpY2F0aW9uU3R5bGVzID0ge1xuICBzdWNjZXNzOiBcImJvcmRlci1ncmVlbi0yMDAgYmctZ3JlZW4tNTAgdGV4dC1ncmVlbi05MDAgZGFyazpib3JkZXItZ3JlZW4tODAwIGRhcms6YmctZ3JlZW4tOTUwIGRhcms6dGV4dC1ncmVlbi0xMDBcIixcbiAgZXJyb3I6IFwiYm9yZGVyLXJlZC0yMDAgYmctcmVkLTUwIHRleHQtcmVkLTkwMCBkYXJrOmJvcmRlci1yZWQtODAwIGRhcms6YmctcmVkLTk1MCBkYXJrOnRleHQtcmVkLTEwMFwiLFxuICB3YXJuaW5nOiBcImJvcmRlci15ZWxsb3ctMjAwIGJnLXllbGxvdy01MCB0ZXh0LXllbGxvdy05MDAgZGFyazpib3JkZXIteWVsbG93LTgwMCBkYXJrOmJnLXllbGxvdy05NTAgZGFyazp0ZXh0LXllbGxvdy0xMDBcIixcbiAgaW5mbzogXCJib3JkZXItYmx1ZS0yMDAgYmctYmx1ZS01MCB0ZXh0LWJsdWUtOTAwIGRhcms6Ym9yZGVyLWJsdWUtODAwIGRhcms6YmctYmx1ZS05NTAgZGFyazp0ZXh0LWJsdWUtMTAwXCIsXG59XG5cbmNvbnN0IHBvc2l0aW9uU3R5bGVzID0ge1xuICBcInRvcC1yaWdodFwiOiBcInRvcC00IHJpZ2h0LTRcIixcbiAgXCJ0b3AtbGVmdFwiOiBcInRvcC00IGxlZnQtNFwiLFxuICBcImJvdHRvbS1yaWdodFwiOiBcImJvdHRvbS00IHJpZ2h0LTRcIixcbiAgXCJib3R0b20tbGVmdFwiOiBcImJvdHRvbS00IGxlZnQtNFwiLFxuICBcInRvcC1jZW50ZXJcIjogXCJ0b3AtNCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMlwiLFxuICBcImJvdHRvbS1jZW50ZXJcIjogXCJib3R0b20tNCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMlwiLFxufVxuXG5leHBvcnQgZnVuY3Rpb24gTm90aWZpY2F0aW9uKHtcbiAgaWQsXG4gIHR5cGUgPSBcImluZm9cIixcbiAgdGl0bGUsXG4gIG1lc3NhZ2UsXG4gIGR1cmF0aW9uID0gNDAwMCxcbiAgb25DbG9zZSxcbiAgY2xvc2FibGUgPSB0cnVlLFxuICBwb3NpdGlvbiA9IFwidG9wLXJpZ2h0XCIsXG59OiBOb3RpZmljYXRpb25Qcm9wcykge1xuICBjb25zdCBbaXNWaXNpYmxlLCBzZXRJc1Zpc2libGVdID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2lzTGVhdmluZywgc2V0SXNMZWF2aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IEljb24gPSBub3RpZmljYXRpb25JY29uc1t0eXBlXVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGR1cmF0aW9uID4gMCkge1xuICAgICAgY29uc3QgdGltZXIgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgaGFuZGxlQ2xvc2UoKVxuICAgICAgfSwgZHVyYXRpb24pXG5cbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZXIpXG4gICAgfVxuICB9LCBbZHVyYXRpb25dKVxuXG4gIGNvbnN0IGhhbmRsZUNsb3NlID0gKCkgPT4ge1xuICAgIHNldElzTGVhdmluZyh0cnVlKVxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgc2V0SXNWaXNpYmxlKGZhbHNlKVxuICAgICAgb25DbG9zZT8uKClcbiAgICB9LCAzMDApIC8vIEFuaW1hdGlvbiBkdXJhdGlvblxuICB9XG5cbiAgaWYgKCFpc1Zpc2libGUpIHJldHVybiBudWxsXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZpeGVkIHotNTAgdy1mdWxsIG1heC13LXNtIHAtNCBib3JkZXIgcm91bmRlZC1sZyBzaGFkb3ctbGcgYmFja2Ryb3AtYmx1ci1zbVwiLFxuICAgICAgICBcInRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBlYXNlLWluLW91dFwiLFxuICAgICAgICAhaXNMZWF2aW5nICYmIFwiYW5pbWF0ZS1zbGlkZUluRnJvbVRvcFwiLFxuICAgICAgICBpc0xlYXZpbmcgJiYgXCJhbmltYXRlLXNsaWRlT3V0VG9SaWdodFwiLFxuICAgICAgICBub3RpZmljYXRpb25TdHlsZXNbdHlwZV0sXG4gICAgICAgIHBvc2l0aW9uU3R5bGVzW3Bvc2l0aW9uXVxuICAgICAgKX1cbiAgICAgIHN0eWxlPXt7XG4gICAgICAgIGFuaW1hdGlvbjogIWlzTGVhdmluZ1xuICAgICAgICAgID8gXCJzbGlkZUluRnJvbVRvcCAwLjNzIGVhc2Utb3V0XCJcbiAgICAgICAgICA6IFwic2xpZGVPdXRUb1JpZ2h0IDAuM3MgZWFzZS1pblwiXG4gICAgICB9fVxuICAgICAgcm9sZT1cImFsZXJ0XCJcbiAgICAgIGFyaWEtbGl2ZT1cInBvbGl0ZVwiXG4gICAgPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGdhcC0zXCI+XG4gICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgbXQtMC41IGZsZXgtc2hyaW5rLTBcIiAvPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICB7dGl0bGUgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtIG1iLTFcIj57dGl0bGV9PC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc21cIj57bWVzc2FnZX08L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAge2Nsb3NhYmxlICYmIChcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbG9zZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgIFwiZmxleC1zaHJpbmstMCBwLTEgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIixcbiAgICAgICAgICAgICAgXCJob3ZlcjpiZy1ibGFjay8xMCBkYXJrOmhvdmVyOmJnLXdoaXRlLzEwXCIsXG4gICAgICAgICAgICAgIFwiZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctY3VycmVudFwiLFxuICAgICAgICAgICAgICBcInRleHQtY3VycmVudC83MCBob3Zlcjp0ZXh0LWN1cnJlbnRcIlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJDbG9zZSBub3RpZmljYXRpb25cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cblxuLy8gTm90aWZpY2F0aW9uIE1hbmFnZXIgZm9yIHByb2dyYW1tYXRpYyB1c2FnZVxuY2xhc3MgTm90aWZpY2F0aW9uTWFuYWdlciB7XG4gIHByaXZhdGUgbm90aWZpY2F0aW9uczogTWFwPHN0cmluZywgTm90aWZpY2F0aW9uUHJvcHM+ID0gbmV3IE1hcCgpXG4gIHByaXZhdGUgbGlzdGVuZXJzOiBTZXQ8KCkgPT4gdm9pZD4gPSBuZXcgU2V0KClcblxuICBwcml2YXRlIGdlbmVyYXRlSWQoKTogc3RyaW5nIHtcbiAgICByZXR1cm4gYG5vdGlmaWNhdGlvbi0ke0RhdGUubm93KCl9LSR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWBcbiAgfVxuXG4gIHByaXZhdGUgbm90aWZ5KCkge1xuICAgIHRoaXMubGlzdGVuZXJzLmZvckVhY2gobGlzdGVuZXIgPT4gbGlzdGVuZXIoKSlcbiAgfVxuXG4gIHNob3cobm90aWZpY2F0aW9uOiBPbWl0PE5vdGlmaWNhdGlvblByb3BzLCAnaWQnIHwgJ29uQ2xvc2UnPik6IHN0cmluZyB7XG4gICAgY29uc3QgaWQgPSB0aGlzLmdlbmVyYXRlSWQoKVxuICAgIGNvbnN0IG5vdGlmaWNhdGlvbldpdGhJZDogTm90aWZpY2F0aW9uUHJvcHMgPSB7XG4gICAgICAuLi5ub3RpZmljYXRpb24sXG4gICAgICBpZCxcbiAgICAgIG9uQ2xvc2U6ICgpID0+IHRoaXMucmVtb3ZlKGlkKSxcbiAgICB9XG5cbiAgICB0aGlzLm5vdGlmaWNhdGlvbnMuc2V0KGlkLCBub3RpZmljYXRpb25XaXRoSWQpXG4gICAgdGhpcy5ub3RpZnkoKVxuXG4gICAgcmV0dXJuIGlkXG4gIH1cblxuICByZW1vdmUoaWQ6IHN0cmluZyk6IHZvaWQge1xuICAgIHRoaXMubm90aWZpY2F0aW9ucy5kZWxldGUoaWQpXG4gICAgdGhpcy5ub3RpZnkoKVxuICB9XG5cbiAgY2xlYXIoKTogdm9pZCB7XG4gICAgdGhpcy5ub3RpZmljYXRpb25zLmNsZWFyKClcbiAgICB0aGlzLm5vdGlmeSgpXG4gIH1cblxuICBnZXRBbGwoKTogTm90aWZpY2F0aW9uUHJvcHNbXSB7XG4gICAgcmV0dXJuIEFycmF5LmZyb20odGhpcy5ub3RpZmljYXRpb25zLnZhbHVlcygpKVxuICB9XG5cbiAgc3Vic2NyaWJlKGxpc3RlbmVyOiAoKSA9PiB2b2lkKTogKCkgPT4gdm9pZCB7XG4gICAgdGhpcy5saXN0ZW5lcnMuYWRkKGxpc3RlbmVyKVxuICAgIHJldHVybiAoKSA9PiB0aGlzLmxpc3RlbmVycy5kZWxldGUobGlzdGVuZXIpXG4gIH1cblxuICAvLyBDb252ZW5pZW5jZSBtZXRob2RzXG4gIHN1Y2Nlc3MobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogUGFydGlhbDxOb3RpZmljYXRpb25Qcm9wcz4pOiBzdHJpbmcge1xuICAgIHJldHVybiB0aGlzLnNob3coeyAuLi5vcHRpb25zLCB0eXBlOiBcInN1Y2Nlc3NcIiwgbWVzc2FnZSB9KVxuICB9XG5cbiAgZXJyb3IobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogUGFydGlhbDxOb3RpZmljYXRpb25Qcm9wcz4pOiBzdHJpbmcge1xuICAgIHJldHVybiB0aGlzLnNob3coeyAuLi5vcHRpb25zLCB0eXBlOiBcImVycm9yXCIsIG1lc3NhZ2UgfSlcbiAgfVxuXG4gIHdhcm5pbmcobWVzc2FnZTogc3RyaW5nLCBvcHRpb25zPzogUGFydGlhbDxOb3RpZmljYXRpb25Qcm9wcz4pOiBzdHJpbmcge1xuICAgIHJldHVybiB0aGlzLnNob3coeyAuLi5vcHRpb25zLCB0eXBlOiBcIndhcm5pbmdcIiwgbWVzc2FnZSB9KVxuICB9XG5cbiAgaW5mbyhtZXNzYWdlOiBzdHJpbmcsIG9wdGlvbnM/OiBQYXJ0aWFsPE5vdGlmaWNhdGlvblByb3BzPik6IHN0cmluZyB7XG4gICAgcmV0dXJuIHRoaXMuc2hvdyh7IC4uLm9wdGlvbnMsIHR5cGU6IFwiaW5mb1wiLCBtZXNzYWdlIH0pXG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IG5vdGlmaWNhdGlvbk1hbmFnZXIgPSBuZXcgTm90aWZpY2F0aW9uTWFuYWdlcigpXG5cbi8vIEhvb2sgZm9yIHVzaW5nIG5vdGlmaWNhdGlvbnMgaW4gUmVhY3QgY29tcG9uZW50c1xuZXhwb3J0IGZ1bmN0aW9uIHVzZU5vdGlmaWNhdGlvbnMoKSB7XG4gIGNvbnN0IFtub3RpZmljYXRpb25zLCBzZXROb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlPE5vdGlmaWNhdGlvblByb3BzW10+KFtdKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlTm90aWZpY2F0aW9ucyA9ICgpID0+IHtcbiAgICAgIHNldE5vdGlmaWNhdGlvbnMobm90aWZpY2F0aW9uTWFuYWdlci5nZXRBbGwoKSlcbiAgICB9XG5cbiAgICB1cGRhdGVOb3RpZmljYXRpb25zKClcbiAgICBjb25zdCB1bnN1YnNjcmliZSA9IG5vdGlmaWNhdGlvbk1hbmFnZXIuc3Vic2NyaWJlKHVwZGF0ZU5vdGlmaWNhdGlvbnMpXG5cbiAgICByZXR1cm4gdW5zdWJzY3JpYmVcbiAgfSwgW10pXG5cbiAgcmV0dXJuIHtcbiAgICBub3RpZmljYXRpb25zLFxuICAgIHNob3c6IG5vdGlmaWNhdGlvbk1hbmFnZXIuc2hvdy5iaW5kKG5vdGlmaWNhdGlvbk1hbmFnZXIpLFxuICAgIHJlbW92ZTogbm90aWZpY2F0aW9uTWFuYWdlci5yZW1vdmUuYmluZChub3RpZmljYXRpb25NYW5hZ2VyKSxcbiAgICBjbGVhcjogbm90aWZpY2F0aW9uTWFuYWdlci5jbGVhci5iaW5kKG5vdGlmaWNhdGlvbk1hbmFnZXIpLFxuICAgIHN1Y2Nlc3M6IG5vdGlmaWNhdGlvbk1hbmFnZXIuc3VjY2Vzcy5iaW5kKG5vdGlmaWNhdGlvbk1hbmFnZXIpLFxuICAgIGVycm9yOiBub3RpZmljYXRpb25NYW5hZ2VyLmVycm9yLmJpbmQobm90aWZpY2F0aW9uTWFuYWdlciksXG4gICAgd2FybmluZzogbm90aWZpY2F0aW9uTWFuYWdlci53YXJuaW5nLmJpbmQobm90aWZpY2F0aW9uTWFuYWdlciksXG4gICAgaW5mbzogbm90aWZpY2F0aW9uTWFuYWdlci5pbmZvLmJpbmQobm90aWZpY2F0aW9uTWFuYWdlciksXG4gIH1cbn1cblxuLy8gTm90aWZpY2F0aW9uIENvbnRhaW5lciBDb21wb25lbnRcbmV4cG9ydCBmdW5jdGlvbiBOb3RpZmljYXRpb25Db250YWluZXIoKSB7XG4gIGNvbnN0IHsgbm90aWZpY2F0aW9ucyB9ID0gdXNlTm90aWZpY2F0aW9ucygpXG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAge25vdGlmaWNhdGlvbnMubWFwKChub3RpZmljYXRpb24pID0+IChcbiAgICAgICAgPE5vdGlmaWNhdGlvbiBrZXk9e25vdGlmaWNhdGlvbi5pZH0gey4uLm5vdGlmaWNhdGlvbn0gLz5cbiAgICAgICkpfVxuICAgIDwvPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJYIiwiQ2hlY2tDaXJjbGUiLCJBbGVydENpcmNsZSIsIkluZm8iLCJBbGVydFRyaWFuZ2xlIiwiY24iLCJub3RpZmljYXRpb25JY29ucyIsInN1Y2Nlc3MiLCJlcnJvciIsIndhcm5pbmciLCJpbmZvIiwibm90aWZpY2F0aW9uU3R5bGVzIiwicG9zaXRpb25TdHlsZXMiLCJOb3RpZmljYXRpb24iLCJpZCIsInR5cGUiLCJ0aXRsZSIsIm1lc3NhZ2UiLCJkdXJhdGlvbiIsIm9uQ2xvc2UiLCJjbG9zYWJsZSIsInBvc2l0aW9uIiwiaXNWaXNpYmxlIiwic2V0SXNWaXNpYmxlIiwiaXNMZWF2aW5nIiwic2V0SXNMZWF2aW5nIiwiSWNvbiIsInRpbWVyIiwic2V0VGltZW91dCIsImhhbmRsZUNsb3NlIiwiY2xlYXJUaW1lb3V0IiwiZGl2IiwiY2xhc3NOYW1lIiwic3R5bGUiLCJhbmltYXRpb24iLCJyb2xlIiwiYXJpYS1saXZlIiwiYnV0dG9uIiwib25DbGljayIsImFyaWEtbGFiZWwiLCJOb3RpZmljYXRpb25NYW5hZ2VyIiwiZ2VuZXJhdGVJZCIsIkRhdGUiLCJub3ciLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJub3RpZnkiLCJsaXN0ZW5lcnMiLCJmb3JFYWNoIiwibGlzdGVuZXIiLCJzaG93Iiwibm90aWZpY2F0aW9uIiwibm90aWZpY2F0aW9uV2l0aElkIiwicmVtb3ZlIiwibm90aWZpY2F0aW9ucyIsInNldCIsImRlbGV0ZSIsImNsZWFyIiwiZ2V0QWxsIiwiQXJyYXkiLCJmcm9tIiwidmFsdWVzIiwic3Vic2NyaWJlIiwiYWRkIiwib3B0aW9ucyIsIk1hcCIsIlNldCIsIm5vdGlmaWNhdGlvbk1hbmFnZXIiLCJ1c2VOb3RpZmljYXRpb25zIiwic2V0Tm90aWZpY2F0aW9ucyIsInVwZGF0ZU5vdGlmaWNhdGlvbnMiLCJ1bnN1YnNjcmliZSIsImJpbmQiLCJOb3RpZmljYXRpb25Db250YWluZXIiLCJtYXAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/notification.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        closeButton: true,\n        duration: 4000,\n        position: \"top-right\",\n        expand: true,\n        richColors: true,\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg group-[.toaster]:rounded-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\n                closeButton: \"group-[.toast]:bg-background group-[.toast]:text-foreground group-[.toast]:border-border group-[.toast]:hover:bg-muted group-[.toast]:focus:ring-2 group-[.toast]:focus:ring-ring group-[.toast]:focus:ring-offset-2\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRXNDO0FBQ0k7QUFJMUMsTUFBTUMsVUFBVSxDQUFDLEVBQUUsR0FBR0UsT0FBcUI7SUFDekMsTUFBTSxFQUFFQyxRQUFRLFFBQVEsRUFBRSxHQUFHSixxREFBUUE7SUFFckMscUJBQ0UsOERBQUNFLDJDQUFNQTtRQUNMRSxPQUFPQTtRQUNQQyxXQUFVO1FBQ1ZDLGFBQWE7UUFDYkMsVUFBVTtRQUNWQyxVQUFTO1FBQ1RDLFFBQVE7UUFDUkMsWUFBWTtRQUNaQyxjQUFjO1lBQ1pDLFlBQVk7Z0JBQ1ZDLE9BQ0U7Z0JBQ0ZDLGFBQWE7Z0JBQ2JDLGNBQ0U7Z0JBQ0ZDLGNBQ0U7Z0JBQ0ZWLGFBQ0U7WUFDSjtRQUNGO1FBQ0MsR0FBR0gsS0FBSzs7Ozs7O0FBR2Y7QUFFa0IiLCJzb3VyY2VzIjpbIkQ6XFxzdGVwLWJ5LXN0ZXBcXHRoZXBhcGVyYnVsbC0xNDRcXGNvbXBvbmVudHNcXHVpXFxzb25uZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVRoZW1lIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcbmltcG9ydCB7IFRvYXN0ZXIgYXMgU29ubmVyIH0gZnJvbSBcInNvbm5lclwiXG5cbnR5cGUgVG9hc3RlclByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNvbm5lcj5cblxuY29uc3QgVG9hc3RlciA9ICh7IC4uLnByb3BzIH06IFRvYXN0ZXJQcm9wcykgPT4ge1xuICBjb25zdCB7IHRoZW1lID0gXCJzeXN0ZW1cIiB9ID0gdXNlVGhlbWUoKVxuXG4gIHJldHVybiAoXG4gICAgPFNvbm5lclxuICAgICAgdGhlbWU9e3RoZW1lIGFzIFRvYXN0ZXJQcm9wc1tcInRoZW1lXCJdfVxuICAgICAgY2xhc3NOYW1lPVwidG9hc3RlciBncm91cFwiXG4gICAgICBjbG9zZUJ1dHRvbj17dHJ1ZX1cbiAgICAgIGR1cmF0aW9uPXs0MDAwfVxuICAgICAgcG9zaXRpb249XCJ0b3AtcmlnaHRcIlxuICAgICAgZXhwYW5kPXt0cnVlfVxuICAgICAgcmljaENvbG9ycz17dHJ1ZX1cbiAgICAgIHRvYXN0T3B0aW9ucz17e1xuICAgICAgICBjbGFzc05hbWVzOiB7XG4gICAgICAgICAgdG9hc3Q6XG4gICAgICAgICAgICBcImdyb3VwIHRvYXN0IGdyb3VwLVsudG9hc3Rlcl06YmctYmFja2dyb3VuZCBncm91cC1bLnRvYXN0ZXJdOnRleHQtZm9yZWdyb3VuZCBncm91cC1bLnRvYXN0ZXJdOmJvcmRlci1ib3JkZXIgZ3JvdXAtWy50b2FzdGVyXTpzaGFkb3ctbGcgZ3JvdXAtWy50b2FzdGVyXTpyb3VuZGVkLWxnXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwiZ3JvdXAtWy50b2FzdF06dGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgICAgYWN0aW9uQnV0dG9uOlxuICAgICAgICAgICAgXCJncm91cC1bLnRvYXN0XTpiZy1wcmltYXJ5IGdyb3VwLVsudG9hc3RdOnRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kXCIsXG4gICAgICAgICAgY2FuY2VsQnV0dG9uOlxuICAgICAgICAgICAgXCJncm91cC1bLnRvYXN0XTpiZy1tdXRlZCBncm91cC1bLnRvYXN0XTp0ZXh0LW11dGVkLWZvcmVncm91bmRcIixcbiAgICAgICAgICBjbG9zZUJ1dHRvbjpcbiAgICAgICAgICAgIFwiZ3JvdXAtWy50b2FzdF06YmctYmFja2dyb3VuZCBncm91cC1bLnRvYXN0XTp0ZXh0LWZvcmVncm91bmQgZ3JvdXAtWy50b2FzdF06Ym9yZGVyLWJvcmRlciBncm91cC1bLnRvYXN0XTpob3ZlcjpiZy1tdXRlZCBncm91cC1bLnRvYXN0XTpmb2N1czpyaW5nLTIgZ3JvdXAtWy50b2FzdF06Zm9jdXM6cmluZy1yaW5nIGdyb3VwLVsudG9hc3RdOmZvY3VzOnJpbmctb2Zmc2V0LTJcIixcbiAgICAgICAgfSxcbiAgICAgIH19XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuXG5leHBvcnQgeyBUb2FzdGVyIH1cbiJdLCJuYW1lcyI6WyJ1c2VUaGVtZSIsIlRvYXN0ZXIiLCJTb25uZXIiLCJwcm9wcyIsInRoZW1lIiwiY2xhc3NOYW1lIiwiY2xvc2VCdXR0b24iLCJkdXJhdGlvbiIsInBvc2l0aW9uIiwiZXhwYW5kIiwicmljaENvbG9ycyIsInRvYXN0T3B0aW9ucyIsImNsYXNzTmFtZXMiLCJ0b2FzdCIsImRlc2NyaXB0aW9uIiwiYWN0aW9uQnV0dG9uIiwiY2FuY2VsQnV0dG9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/auth-context.tsx":
/*!***********************************!*\
  !*** ./contexts/auth-context.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./lib/firebase.ts\");\n/* harmony import */ var _lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-utils */ \"(ssr)/./lib/auth-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, {\n                \"AuthProvider.useEffect.unsubscribe\": (user)=>{\n                    setUser(user);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect.unsubscribe\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const handleSignIn = async (email, password)=>{\n        try {\n            const user = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signInWithEmail)(email, password);\n            return user;\n        } catch (error) {\n            console.error('Sign in error:', error);\n            throw error;\n        }\n    };\n    const handleSignOut = async ()=>{\n        try {\n            await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signOutUser)();\n        } catch (error) {\n            console.error('Sign out error:', error);\n            throw error;\n        }\n    };\n    const handleGoogleSignIn = async ()=>{\n        try {\n            const user = await (0,_lib_auth_utils__WEBPACK_IMPORTED_MODULE_4__.signInWithGoogle)();\n            return user;\n        } catch (error) {\n            console.error('Google sign in error:', error);\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        loading,\n        signIn: handleSignIn,\n        signOut: handleSignOut,\n        signInWithGoogle: handleGoogleSignIn\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 68,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./contexts/theme-context.tsx":
/*!************************************!*\
  !*** ./contexts/theme-context.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [colorScheme, setColorScheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"emerald\");\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize theme and color scheme from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            const storedTheme = localStorage.getItem(\"theme\");\n            const storedColorScheme = localStorage.getItem(\"colorScheme\");\n            if (storedTheme) {\n                setTheme(storedTheme);\n            }\n            if (storedColorScheme) {\n                setColorScheme(storedColorScheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update localStorage when theme or color scheme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            localStorage.setItem(\"theme\", theme);\n            localStorage.setItem(\"colorScheme\", colorScheme);\n            // Apply theme to document\n            if (theme === \"dark\" || theme === \"system\" && window.matchMedia(\"(prefers-color-scheme: dark)\").matches) {\n                document.documentElement.classList.add(\"dark\");\n                setResolvedTheme(\"dark\");\n            } else {\n                document.documentElement.classList.remove(\"dark\");\n                setResolvedTheme(\"light\");\n            }\n            // Apply color scheme to document\n            document.documentElement.setAttribute(\"data-color-scheme\", colorScheme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        colorScheme,\n        mounted\n    ]);\n    // Listen for system theme changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            if (theme !== \"system\") return;\n            const mediaQuery = window.matchMedia(\"(prefers-color-scheme: dark)\");\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (mediaQuery.matches) {\n                        document.documentElement.classList.add(\"dark\");\n                        setResolvedTheme(\"dark\");\n                    } else {\n                        document.documentElement.classList.remove(\"dark\");\n                        setResolvedTheme(\"light\");\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener(\"change\", handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener(\"change\", handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    // Avoid hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            setTheme,\n            colorScheme,\n            setColorScheme,\n            resolvedTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\step-by-step\\\\thepaperbull-144\\\\contexts\\\\theme-context.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error(\"useTheme must be used within a ThemeProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./contexts/theme-context.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/auth-utils.ts":
/*!***************************!*\
  !*** ./lib/auth-utils.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthErrorMessage: () => (/* binding */ getAuthErrorMessage),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   isAuthenticated: () => (/* binding */ isAuthenticated),\n/* harmony export */   resetPassword: () => (/* binding */ resetPassword),\n/* harmony export */   signInWithEmail: () => (/* binding */ signInWithEmail),\n/* harmony export */   signInWithGoogle: () => (/* binding */ signInWithGoogle),\n/* harmony export */   signOutUser: () => (/* binding */ signOutUser),\n/* harmony export */   signUpWithEmail: () => (/* binding */ signUpWithEmail)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./firebase */ \"(ssr)/./lib/firebase.ts\");\n\n\n// Google Auth Provider\nconst googleProvider = new firebase_auth__WEBPACK_IMPORTED_MODULE_0__.GoogleAuthProvider();\ngoogleProvider.setCustomParameters({\n    prompt: 'select_account'\n});\n// Sign up with email and password\nconst signUpWithEmail = async (email, password, displayName)=>{\n    try {\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, email, password);\n        // Update display name if provided\n        if (displayName && userCredential.user) {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.updateProfile)(userCredential.user, {\n                displayName: displayName\n            });\n        }\n        return userCredential.user;\n    } catch (error) {\n        console.error('Sign up error:', error);\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Sign in with email and password\nconst signInWithEmail = async (email, password)=>{\n    try {\n        const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, email, password);\n        return userCredential.user;\n    } catch (error) {\n        console.error('Sign in error:', error);\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Sign in with Google\nconst signInWithGoogle = async ()=>{\n    try {\n        const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithPopup)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, googleProvider);\n        return result.user;\n    } catch (error) {\n        console.error('Google sign in error:', error);\n        // Don't throw an error for user-cancelled actions\n        if (error.code === 'auth/popup-closed-by-user' || error.code === 'auth/cancelled-popup-request') {\n            throw error // Re-throw the original error so the UI can handle it appropriately\n            ;\n        }\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Sign out\nconst signOutUser = async ()=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth);\n    } catch (error) {\n        console.error('Sign out error:', error);\n        throw new Error('Failed to sign out');\n    }\n};\n// Reset password\nconst resetPassword = async (email)=>{\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.sendPasswordResetEmail)(_firebase__WEBPACK_IMPORTED_MODULE_1__.auth, email);\n    } catch (error) {\n        console.error('Password reset error:', error);\n        throw new Error(getAuthErrorMessage(error.code));\n    }\n};\n// Get user-friendly error messages\nconst getAuthErrorMessage = (errorCode)=>{\n    switch(errorCode){\n        case 'auth/user-not-found':\n            return 'No account found with this email address. Please check your email or sign up for a new account.';\n        case 'auth/wrong-password':\n            return 'Incorrect password. Please try again or reset your password.';\n        case 'auth/invalid-credential':\n            return 'Invalid email or password. Please check your credentials and try again.';\n        case 'auth/email-already-in-use':\n            return 'An account with this email already exists. Please sign in instead.';\n        case 'auth/weak-password':\n            return 'Password should be at least 6 characters long.';\n        case 'auth/invalid-email':\n            return 'Please enter a valid email address.';\n        case 'auth/user-disabled':\n            return 'This account has been disabled. Please contact support.';\n        case 'auth/too-many-requests':\n            return 'Too many failed attempts. Please try again later or reset your password.';\n        case 'auth/popup-closed-by-user':\n            return 'Sign-in popup was closed. Please try again.';\n        case 'auth/popup-blocked':\n            return 'Sign-in popup was blocked. Please allow popups and try again.';\n        case 'auth/network-request-failed':\n            return 'Network error. Please check your internet connection and try again.';\n        case 'auth/cancelled-popup-request':\n            return 'Sign-in was cancelled.';\n        case 'auth/account-exists-with-different-credential':\n            return 'An account already exists with the same email address but different sign-in credentials.';\n        case 'auth/operation-not-allowed':\n            return 'This sign-in method is not enabled. Please contact support.';\n        case 'auth/invalid-api-key':\n            return 'Invalid API key. Please check your Firebase configuration.';\n        case 'auth/app-deleted':\n            return 'Firebase app has been deleted. Please check your configuration.';\n        case 'auth/invalid-user-token':\n            return 'User token is invalid. Please sign in again.';\n        case 'auth/user-token-expired':\n            return 'User token has expired. Please sign in again.';\n        default:\n            return `Authentication error: ${errorCode || 'Unknown error'}`;\n    }\n};\n// Check if user is authenticated\nconst isAuthenticated = ()=>{\n    return _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser !== null;\n};\n// Get current user\nconst getCurrentUser = ()=>{\n    return _firebase__WEBPACK_IMPORTED_MODULE_1__.auth.currentUser;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-utils.ts\n");

/***/ }),

/***/ "(ssr)/./lib/firebase.ts":
/*!*************************!*\
  !*** ./lib/firebase.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analytics: () => (/* binding */ analytics),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_analytics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/analytics */ \"(ssr)/./node_modules/firebase/analytics/dist/index.mjs\");\n// Firebase client-side configuration\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyBYTl5SiY2ARvKUCiMBxf8zNUBQRu3hg1s\",\n    authDomain: \"thepaperbull-144.firebaseapp.com\",\n    projectId: \"thepaperbull-144\",\n    storageBucket: \"thepaperbull-144.firebasestorage.app\",\n    messagingSenderId: \"540770032311\",\n    appId: \"1:540770032311:web:54b0d4ec1715779408cb32\",\n    measurementId: \"G-5KTY505WKQ\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)().length === 0 ? (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig) : (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApps)()[0];\n// Initialize Firebase Auth\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\n// Initialize Firestore\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\n// Initialize Analytics (only in browser)\nconst analytics =  false ? 0 : null;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/firebase.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcc3RlcC1ieS1zdGVwXFx0aGVwYXBlcmJ1bGwtMTQ0XFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers.tsx */ \"(ssr)/./components/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/notification.tsx */ \"(ssr)/./components/ui/notification.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/sonner.tsx */ \"(ssr)/./components/ui/sonner.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Cnotification.tsx%22%2C%22ids%22%3A%5B%22NotificationContainer%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cstep-by-step%5C%5Cthepaperbull-144%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/protobufjs","vendor-chunks/tailwind-merge","vendor-chunks/sonner","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lucide-react","vendor-chunks/lodash.camelcase","vendor-chunks/tslib","vendor-chunks/idb","vendor-chunks/next-themes","vendor-chunks/firebase","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5Cstep-by-step%5Cthepaperbull-144%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cstep-by-step%5Cthepaperbull-144&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
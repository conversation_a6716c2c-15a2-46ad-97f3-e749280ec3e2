// Firestore service for managing user data
import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  collection,
  addDoc,
  query,
  where,
  orderBy,
  onSnapshot,
  deleteDoc,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'
import { db } from './firebase'
import { User } from '@/types/user'
import { Position, Order, Trade, AccountInfo } from '@/types/trading'

// Default values
const DEFAULT_USER_BALANCE = 10000
const FREE_TIER_MAX_BALANCE = 50000

export interface UserProfile {
  id: string
  email: string
  name: string
  avatar?: string
  phone?: string
  country?: string
  bio?: string
  createdAt: Timestamp
  subscription: {
    type: 'free' | 'premium' | 'pro'
    status: 'active' | 'inactive' | 'cancelled'
    startDate: Timestamp
    endDate?: Timestamp
    features: string[]
  }
  balance: {
    current: number
    maximum: number
    currency: string
    lastUpdated: Timestamp
  }
  preferences?: {
    favoriteMarkets: string[]
    theme: 'light' | 'dark' | 'system'
    colorScheme: string
    rememberedUsername?: string
  }
  settings: {
    theme: 'light' | 'dark' | 'system'
    notifications: {
      email: boolean
      push: boolean
      trading_alerts: boolean
      price_alerts: boolean
      news_updates: boolean
    }
    trading: {
      default_leverage: number
      risk_management: boolean
      auto_close_positions: boolean
      confirmation_dialogs: boolean
    }
  }
}

export interface Transaction {
  id: string
  userId: string
  type: 'deposit' | 'withdrawal' | 'trade' | 'fee'
  amount: number
  balance_before: number
  balance_after: number
  description: string
  timestamp: Timestamp
  metadata?: Record<string, any>
}

export interface UserPosition extends Position {
  userId: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface UserOrder extends Order {
  userId: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface UserTrade extends Trade {
  userId: string
  createdAt: Timestamp
}

class FirestoreService {
  // User Profile Management
  async createUserProfile(userId: string, email: string, name: string): Promise<UserProfile> {
    const now = serverTimestamp()

    const userProfile: Omit<UserProfile, 'id'> = {
      email,
      name,
      avatar: 'bull-trader', // Default avatar
      createdAt: now as Timestamp,
      subscription: {
        type: 'free',
        status: 'active',
        startDate: now as Timestamp,
        features: ['basic_trading', 'paper_trading', 'basic_charts']
      },
      balance: {
        current: DEFAULT_USER_BALANCE,
        maximum: FREE_TIER_MAX_BALANCE,
        currency: 'USDT',
        lastUpdated: now as Timestamp
      },
      settings: {
        theme: 'system',
        notifications: {
          email: true,
          push: true,
          trading_alerts: true,
          price_alerts: true,
          news_updates: false
        },
        trading: {
          default_leverage: 10,
          risk_management: true,
          auto_close_positions: false,
          confirmation_dialogs: true
        }
      }
    }

    await setDoc(doc(db, 'users', userId), userProfile)

    // Create initial deposit transaction
    await this.addTransaction(userId, {
      type: 'deposit',
      amount: DEFAULT_USER_BALANCE,
      balance_before: 0,
      balance_after: DEFAULT_USER_BALANCE,
      description: 'Initial balance'
    })

    return { id: userId, ...userProfile } as UserProfile
  }

  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId))
      if (userDoc.exists()) {
        return { id: userId, ...userDoc.data() } as UserProfile
      }
      return null
    } catch (error) {
      console.error('Error getting user profile:', error)
      return null
    }
  }

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      // Only update balance.lastUpdated if we're actually updating balance
      const updateData = { ...updates }
      if (updates.balance) {
        updateData['balance.lastUpdated'] = serverTimestamp()
      }

      await updateDoc(doc(db, 'users', userId), updateData)
    } catch (error) {
      console.error('Error updating user profile:', error)
      throw error
    }
  }

  async updateUserBalance(userId: string, newBalance: number): Promise<void> {
    try {
      await updateDoc(doc(db, 'users', userId), {
        'balance.current': newBalance,
        'balance.lastUpdated': serverTimestamp()
      })
    } catch (error) {
      console.error('Error updating user balance:', error)
      throw error
    }
  }

  // Transaction Management
  async addTransaction(userId: string, transaction: Omit<Transaction, 'id' | 'userId' | 'timestamp'>): Promise<string> {
    try {
      const transactionData = {
        ...transaction,
        userId,
        timestamp: serverTimestamp()
      }

      const docRef = await addDoc(collection(db, 'transactions'), transactionData)
      return docRef.id
    } catch (error) {
      console.error('Error adding transaction:', error)
      throw error
    }
  }

  async getUserTransactions(userId: string, limit: number = 50): Promise<Transaction[]> {
    try {
      const q = query(
        collection(db, 'transactions'),
        where('userId', '==', userId),
        orderBy('timestamp', 'desc')
      )

      return new Promise((resolve, reject) => {
        const unsubscribe = onSnapshot(q, (snapshot) => {
          const transactions = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as Transaction[]
          resolve(transactions.slice(0, limit))
        }, reject)
      })
    } catch (error) {
      console.error('Error getting user transactions:', error)
      return []
    }
  }

  // Position Management
  async addPosition(userId: string, position: Omit<UserPosition, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      console.log('Adding position to Firestore:', { userId, position })

      const now = serverTimestamp()
      const positionData = {
        ...position,
        userId,
        createdAt: now,
        updatedAt: now
      }

      const docRef = await addDoc(collection(db, 'positions'), positionData)
      console.log('Position added successfully with ID:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('Error adding position to Firestore:', error)

      // Provide more specific error messages
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied. Please check your authentication and try again.')
      } else if (error.code === 'unavailable') {
        throw new Error('Service temporarily unavailable. Please try again in a moment.')
      }

      throw new Error(`Failed to create position: ${error.message || 'Unknown error'}`)
    }
  }

  async updatePosition(positionId: string, updates: Partial<UserPosition>): Promise<void> {
    try {
      await updateDoc(doc(db, 'positions', positionId), {
        ...updates,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error updating position:', error)
      throw error
    }
  }

  async deletePosition(positionId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'positions', positionId))
    } catch (error) {
      console.error('Error deleting position:', error)
      throw error
    }
  }

  async getUserPositions(userId: string): Promise<UserPosition[]> {
    try {
      const q = query(
        collection(db, 'positions'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      )

      return new Promise((resolve, reject) => {
        const unsubscribe = onSnapshot(q, (snapshot) => {
          const positions = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as UserPosition[]
          resolve(positions)
        }, reject)
      })
    } catch (error) {
      console.error('Error getting user positions:', error)
      return []
    }
  }

  // Order Management
  async addOrder(userId: string, order: Omit<UserOrder, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      console.log('Adding order to Firestore:', { userId, order })

      const now = serverTimestamp()
      const orderData = {
        ...order,
        userId,
        createdAt: now,
        updatedAt: now
      }

      const docRef = await addDoc(collection(db, 'orders'), orderData)
      console.log('Order added successfully with ID:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('Error adding order to Firestore:', error)

      // Provide more specific error messages
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied. Please check your authentication and try again.')
      } else if (error.code === 'unavailable') {
        throw new Error('Service temporarily unavailable. Please try again in a moment.')
      } else if (error.code === 'failed-precondition') {
        throw new Error('Database operation failed. Please check your connection and try again.')
      }

      throw new Error(`Failed to place order: ${error.message || 'Unknown error'}`)
    }
  }

  async updateOrder(orderId: string, updates: Partial<UserOrder>): Promise<void> {
    try {
      await updateDoc(doc(db, 'orders', orderId), {
        ...updates,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      console.error('Error updating order:', error)
      throw error
    }
  }

  async deleteOrder(orderId: string): Promise<void> {
    try {
      await deleteDoc(doc(db, 'orders', orderId))
    } catch (error) {
      console.error('Error deleting order:', error)
      throw error
    }
  }

  async getUserOrders(userId: string): Promise<UserOrder[]> {
    try {
      const q = query(
        collection(db, 'orders'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      )

      return new Promise((resolve, reject) => {
        const unsubscribe = onSnapshot(q, (snapshot) => {
          const orders = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as UserOrder[]
          resolve(orders)
        }, reject)
      })
    } catch (error) {
      console.error('Error getting user orders:', error)
      return []
    }
  }

  // Trade History Management
  async addTrade(userId: string, trade: Omit<UserTrade, 'id' | 'userId' | 'createdAt'>): Promise<string> {
    try {
      console.log('Adding trade to Firestore:', { userId, trade })

      const tradeData = {
        ...trade,
        userId,
        createdAt: serverTimestamp()
      }

      const docRef = await addDoc(collection(db, 'trades'), tradeData)
      console.log('Trade added successfully with ID:', docRef.id)
      return docRef.id
    } catch (error) {
      console.error('Error adding trade to Firestore:', error)

      // Provide more specific error messages
      if (error.code === 'permission-denied') {
        throw new Error('Permission denied. Please check your authentication and try again.')
      } else if (error.code === 'unavailable') {
        throw new Error('Service temporarily unavailable. Please try again in a moment.')
      }

      throw new Error(`Failed to record trade: ${error.message || 'Unknown error'}`)
    }
  }

  async getUserTrades(userId: string, limit: number = 100): Promise<UserTrade[]> {
    try {
      const q = query(
        collection(db, 'trades'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc')
      )

      return new Promise((resolve, reject) => {
        const unsubscribe = onSnapshot(q, (snapshot) => {
          const trades = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as UserTrade[]
          unsubscribe() // Unsubscribe immediately after getting the data
          resolve(trades.slice(0, limit))
        }, (error) => {
          reject(error)
        })
      })
    } catch (error) {
      console.error('Error getting user trades:', error)
      return []
    }
  }

  // Real-time subscriptions
  subscribeToUserProfile(userId: string, callback: (profile: UserProfile | null) => void): () => void {
    return onSnapshot(doc(db, 'users', userId), (doc) => {
      if (doc.exists()) {
        callback({ id: userId, ...doc.data() } as UserProfile)
      } else {
        callback(null)
      }
    })
  }

  subscribeToUserPositions(userId: string, callback: (positions: UserPosition[]) => void): () => void {
    const q = query(
      collection(db, 'positions'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    )

    return onSnapshot(q, (snapshot) => {
      const positions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserPosition[]
      callback(positions)
    })
  }

  subscribeToUserOrders(userId: string, callback: (orders: UserOrder[]) => void): () => void {
    const q = query(
      collection(db, 'orders'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    )

    return onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserOrder[]
      callback(orders)
    })
  }

  subscribeToUserTrades(userId: string, callback: (trades: UserTrade[]) => void): () => void {
    const q = query(
      collection(db, 'trades'),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    )

    return onSnapshot(q, (snapshot) => {
      const trades = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserTrade[]
      callback(trades)
    })
  }
}

export const firestoreService = new FirestoreService()
